#!/usr/bin/env python3
"""
Setup database and table for Gazette scraper
"""
import mysql.connector
from mysql.connector import Error

def setup_database():
    """Create database and table if they don't exist"""
    try:
        # Connect to MySQL server (without specifying database)
        config = {
            'host': 'localhost',
            'user': 'root',
            'password': '',
            'port': 3306,
            'charset': 'utf8mb4',
            'autocommit': True
        }
        
        print("Connecting to MySQL server...")
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        # Create database if it doesn't exist
        print("Creating database 'gazette_notices'...")
        cursor.execute("CREATE DATABASE IF NOT EXISTS gazette_notices CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print("✅ Database created/verified")
        
        # Switch to the database
        cursor.execute("USE gazette_notices")
        
        # Create table if it doesn't exist
        print("Creating table 'gazette_notices'...")
        create_table_query = """
        CREATE TABLE IF NOT EXISTS gazette_notices (
            id INT AUTO_INCREMENT PRIMARY KEY,
            url VARCHAR(500) NOT NULL UNIQUE,
            notice_id VARCHAR(100),
            title TEXT,
            company_name VARCHAR(500),
            notice_type VARCHAR(100),
            publication_date DATE,
            notice_date DATE,
            content TEXT,
            court VARCHAR(200),
            case_number VARCHAR(100),
            petitioner TEXT,
            respondent TEXT,
            address TEXT,
            postcode VARCHAR(20),
            amount DECIMAL(15,2),
            status VARCHAR(50) DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_notice_id (notice_id),
            INDEX idx_company_name (company_name),
            INDEX idx_notice_type (notice_type),
            INDEX idx_publication_date (publication_date),
            INDEX idx_postcode (postcode),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        
        cursor.execute(create_table_query)
        print("✅ Table created/verified")
        
        # Verify table structure
        cursor.execute("DESCRIBE gazette_notices")
        columns = cursor.fetchall()
        print("📋 Table structure:")
        for column in columns:
            print(f"   - {column[0]}: {column[1]}")
        
        cursor.close()
        connection.close()
        print("✅ Database setup completed successfully!")
        return True
        
    except Error as e:
        print(f"❌ Database setup failed: {e}")
        return False

if __name__ == "__main__":
    setup_database()
