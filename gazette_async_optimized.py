#!/usr/bin/env python3
"""
Optimized Gazette Scraper with Async Processing and CPU Parallelization
======================================================================

This version demonstrates how to optimize web scraping without GPU acceleration:
1. Async HTTP requests for concurrent page fetching
2. Separate thread pool for HTML parsing to avoid blocking
3. Producer-consumer pattern for optimal resource utilization
4. Maintains the same extraction logic as the original script
"""

import asyncio
import aiohttp
import json
import os
import time
import re
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import logging
from typing import List, Set, Dict, Optional
import sys
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import queue
import threading
from dataclasses import dataclass

# Configuration
FLARESOLVERR_URL = "http://localhost:8191/v1"
BASE_URL = "https://www.thegazette.co.uk/insolvency/notice"
TOTAL_PAGES = 20292
TOTAL_NOTICES = 2029200
RESULTS_PER_PAGE = 100
PAGES_PER_FOLDER = 100

# Concurrency settings
MAX_CONCURRENT_REQUESTS = 10  # Concurrent HTTP requests
MAX_PARSING_WORKERS = 4       # CPU cores for HTML parsing
QUEUE_SIZE = 50              # Buffer size between fetching and parsing

# Output paths
SIMPLE_OUTPUT_FILE = "all_gazette_urls_async.txt"
ORGANIZED_OUTPUT_DIR = "gazette_urls_organized_async"

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('gazette_scraper_async.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class PageData:
    """Container for page data to be processed"""
    page_num: int
    html_content: str
    fetch_time: float

class OptimizedGazetteScraper:
    def __init__(self):
        self.session_id = None
        self.all_urls = set()
        self.processed_pages = set()
        self.failed_pages = set()
        
        # Threading components
        self.page_queue = queue.Queue(maxsize=QUEUE_SIZE)
        self.results_queue = queue.Queue()
        self.parsing_executor = ThreadPoolExecutor(max_workers=MAX_PARSING_WORKERS)
        self.shutdown_event = threading.Event()
        
        # Create output directories
        os.makedirs(ORGANIZED_OUTPUT_DIR, exist_ok=True)
        
        # Load progress if exists
        self.load_progress()

    async def create_session(self, session: aiohttp.ClientSession) -> bool:
        """Create a new FlareSolver session"""
        try:
            payload = {
                "cmd": "sessions.create",
                "session": f"gazette_session_{int(time.time())}"
            }
            async with session.post(FLARESOLVERR_URL, json=payload, timeout=30) as response:
                result = await response.json()
                if result.get("status") == "ok":
                    self.session_id = result["session"]
                    logger.info(f"Created FlareSolver session: {self.session_id}")
                    return True
                else:
                    logger.error(f"Failed to create session: {result}")
                    return False
        except Exception as e:
            logger.error(f"Error creating FlareSolver session: {e}")
            return False

    async def get_page_content_async(self, session: aiohttp.ClientSession, page_num: int) -> Optional[str]:
        """Get page content using FlareSolver asynchronously"""
        url = f"{BASE_URL}?text=&insolvency_corporate=G205010000&insolvency_personal=&location-postcode-1=&location-distance-1=1&location-local-authority-1=&numberOfLocationSearches=1&start-publish-date=&end-publish-date=&edition=&london-issue=&edinburgh-issue=&belfast-issue=&sort-by=oldest-date&results-page-size=100&results-page={page_num}"
        
        payload = {
            "cmd": "request.get",
            "url": url,
            "session": self.session_id,
            "maxTimeout": 60000
        }

        try:
            async with session.post(FLARESOLVERR_URL, json=payload, timeout=70) as response:
                result = await response.json()
                if result.get("status") == "ok":
                    return result["solution"]["response"]
                else:
                    logger.error(f"FlareSolver error for page {page_num}: {result}")
                    return None
        except Exception as e:
            logger.error(f"Error fetching page {page_num}: {e}")
            return None

    def extract_notice_urls(self, html_content: str) -> List[str]:
        """Extract notice URLs from HTML content (same logic as original)"""
        try:
            # Try lxml first, fall back to html.parser if not available
            try:
                soup = BeautifulSoup(html_content, 'lxml')
            except:
                soup = BeautifulSoup(html_content, 'html.parser')

            urls = []
            
            # Find all links that match the notice pattern
            notice_links = soup.find_all('a', href=re.compile(r'^/notice/[^/]+$'))
            
            for link in notice_links:
                href = link.get('href')
                if href and href.startswith('/notice/'):
                    full_url = urljoin("https://www.thegazette.co.uk", href)
                    urls.append(full_url)

            # Remove duplicates while preserving order
            seen = set()
            unique_urls = []
            for url in urls:
                if url not in seen:
                    seen.add(url)
                    unique_urls.append(url)

            return unique_urls
        except Exception as e:
            logger.error(f"Error extracting URLs from HTML: {e}")
            return []

    def process_page_data(self, page_data: PageData) -> Dict:
        """Process page data in a separate thread"""
        try:
            urls = self.extract_notice_urls(page_data.html_content)
            return {
                'page_num': page_data.page_num,
                'urls': urls,
                'success': True,
                'fetch_time': page_data.fetch_time,
                'parse_time': time.time()
            }
        except Exception as e:
            logger.error(f"Error processing page {page_data.page_num}: {e}")
            return {
                'page_num': page_data.page_num,
                'urls': [],
                'success': False,
                'error': str(e)
            }

    async def fetch_pages_producer(self, session: aiohttp.ClientSession, start_page: int, end_page: int):
        """Producer: Fetch pages and put them in queue"""
        semaphore = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)
        
        async def fetch_single_page(page_num: int):
            if page_num in self.processed_pages:
                return
                
            async with semaphore:
                fetch_start = time.time()
                html_content = await self.get_page_content_async(session, page_num)
                
                if html_content:
                    page_data = PageData(
                        page_num=page_num,
                        html_content=html_content,
                        fetch_time=time.time() - fetch_start
                    )
                    
                    # Submit for parsing
                    future = self.parsing_executor.submit(self.process_page_data, page_data)
                    self.results_queue.put((page_num, future))
                    
                    logger.info(f"Fetched page {page_num} in {page_data.fetch_time:.2f}s")
                else:
                    self.failed_pages.add(page_num)
                    logger.error(f"Failed to fetch page {page_num}")
                
                # Small delay to avoid overwhelming the server
                await asyncio.sleep(0.1)

        # Create tasks for all pages
        tasks = []
        for page_num in range(start_page, end_page + 1):
            if page_num not in self.processed_pages:
                task = asyncio.create_task(fetch_single_page(page_num))
                tasks.append(task)
        
        # Wait for all fetch tasks to complete
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # Signal that fetching is complete
        self.results_queue.put((None, None))

    def save_urls_to_organized_structure(self, page_num: int, urls: List[str]):
        """Save URLs to organized folder structure (same as original)"""
        if not urls:
            return

        folder_name = self.get_folder_name(page_num)
        folder_path = os.path.join(ORGANIZED_OUTPUT_DIR, folder_name)
        os.makedirs(folder_path, exist_ok=True)

        file_path = os.path.join(folder_path, f"page_{page_num}_urls.txt")

        try:
            with open(file_path, "w") as f:
                for url in urls:
                    f.write(f"{url}\n")
        except Exception as e:
            logger.error(f"Error saving organized URLs for page {page_num}: {e}")

    def append_urls_to_simple_file(self, urls: List[str]):
        """Append URLs to simple output file (same as original)"""
        if not urls:
            return

        try:
            with open(SIMPLE_OUTPUT_FILE, "a") as f:
                for url in urls:
                    if url not in self.all_urls:
                        f.write(f"{url}\n")
                        self.all_urls.add(url)
        except Exception as e:
            logger.error(f"Error appending URLs to simple file: {e}")

    def get_folder_name(self, page_num: int) -> str:
        """Get folder name for a given page number (same as original)"""
        start_page = ((page_num - 1) // PAGES_PER_FOLDER) * PAGES_PER_FOLDER + 1
        end_page = min(start_page + PAGES_PER_FOLDER - 1, TOTAL_PAGES)
        return f"pages_{start_page}-{end_page}"

    def save_progress(self):
        """Save current progress to file (same as original)"""
        progress_data = {
            "processed_pages": list(self.processed_pages),
            "failed_pages": list(self.failed_pages),
            "total_urls_found": len(self.all_urls),
            "last_updated": time.time()
        }

        try:
            with open("scraper_progress_async.json", "w") as f:
                json.dump(progress_data, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving progress: {e}")

    def load_progress(self):
        """Load previous progress if exists (same as original)"""
        try:
            if os.path.exists("scraper_progress_async.json"):
                with open("scraper_progress_async.json", "r") as f:
                    progress_data = json.load(f)

                self.processed_pages = set(progress_data.get("processed_pages", []))
                self.failed_pages = set(progress_data.get("failed_pages", []))

                logger.info(f"Loaded progress: {len(self.processed_pages)} pages processed")

                # Load existing URLs
                if os.path.exists(SIMPLE_OUTPUT_FILE):
                    with open(SIMPLE_OUTPUT_FILE, "r") as f:
                        for line in f:
                            url = line.strip()
                            if url:
                                self.all_urls.add(url)
                    logger.info(f"Loaded {len(self.all_urls)} existing URLs")
        except Exception as e:
            logger.error(f"Error loading progress: {e}")

    async def run_optimized_scraper(self, start_page: int = 1, end_page: int = None):
        """Run the optimized scraping process"""
        if end_page is None:
            end_page = TOTAL_PAGES

        logger.info(f"Starting optimized Gazette scraper for pages {start_page} to {end_page}")
        logger.info(f"Concurrency: {MAX_CONCURRENT_REQUESTS} requests, {MAX_PARSING_WORKERS} parsing workers")

        async with aiohttp.ClientSession() as session:
            # Create FlareSolver session
            if not await self.create_session(session):
                logger.error("Failed to create FlareSolver session")
                return False

            try:
                # Start the producer task
                producer_task = asyncio.create_task(
                    self.fetch_pages_producer(session, start_page, end_page)
                )

                # Process results as they come in
                processed_count = 0
                while True:
                    try:
                        page_num, future = self.results_queue.get(timeout=1)
                        
                        if page_num is None:  # Signal that fetching is complete
                            break
                            
                        # Get the parsing result
                        result = future.result()
                        
                        if result['success']:
                            urls = result['urls']
                            
                            # Save URLs (same logic as original)
                            self.save_urls_to_organized_structure(page_num, urls)
                            self.append_urls_to_simple_file(urls)
                            
                            # Mark as processed
                            self.processed_pages.add(page_num)
                            processed_count += 1
                            
                            logger.info(f"Processed page {page_num}: {len(urls)} URLs found "
                                      f"(fetch: {result['fetch_time']:.2f}s)")
                            
                            # Save progress periodically
                            if processed_count % 10 == 0:
                                self.save_progress()
                                logger.info(f"Progress: {len(self.processed_pages)}/{end_page - start_page + 1} pages")
                        else:
                            self.failed_pages.add(page_num)
                            logger.error(f"Failed to process page {page_num}")
                            
                    except queue.Empty:
                        continue
                    except Exception as e:
                        logger.error(f"Error processing result: {e}")

                # Wait for producer to complete
                await producer_task
                
                # Final save
                self.save_progress()
                
                logger.info(f"Optimized scraping completed!")
                logger.info(f"Total pages processed: {len(self.processed_pages)}")
                logger.info(f"Total URLs found: {len(self.all_urls)}")
                logger.info(f"Failed pages: {len(self.failed_pages)}")
                
                return True

            except Exception as e:
                logger.error(f"Error during optimized scraping: {e}")
                return False
            finally:
                self.parsing_executor.shutdown(wait=True)

# Example usage
async def main():
    scraper = OptimizedGazetteScraper()
    await scraper.run_optimized_scraper(1, 100)  # Test with first 100 pages

if __name__ == "__main__":
    asyncio.run(main())
