#!/usr/bin/env python3
"""
Test database connection for Gazette scraper
"""
import mysql.connector
from mysql.connector import <PERSON>rror

def test_database_connection():
    """Test MySQL database connection"""
    try:
        # Database configuration
        config = {
            'host': 'localhost',
            'database': 'gazette_notices',
            'user': 'root',
            'password': '',
            'port': 3306,
            'charset': 'utf8mb4',
            'collation': 'utf8mb4_unicode_ci',
            'autocommit': True
        }
        
        print("Testing database connection...")
        connection = mysql.connector.connect(**config)
        
        if connection.is_connected():
            cursor = connection.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"✅ Successfully connected to MySQL Server version: {version[0]}")
            
            # Test if database exists
            cursor.execute("SHOW DATABASES LIKE 'gazette_notices'")
            db_exists = cursor.fetchone()
            if db_exists:
                print("✅ Database 'gazette_notices' exists")
                
                # Test if table exists
                cursor.execute("SHOW TABLES LIKE 'gazette_notices'")
                table_exists = cursor.fetchone()
                if table_exists:
                    print("✅ Table 'gazette_notices' exists")
                    
                    # Check table structure
                    cursor.execute("DESCRIBE gazette_notices")
                    columns = cursor.fetchall()
                    print("📋 Table structure:")
                    for column in columns:
                        print(f"   - {column[0]}: {column[1]}")
                        
                    # Check record count
                    cursor.execute("SELECT COUNT(*) FROM gazette_notices")
                    count = cursor.fetchone()[0]
                    print(f"📊 Current records in table: {count}")
                    
                else:
                    print("❌ Table 'gazette_notices' does not exist")
            else:
                print("❌ Database 'gazette_notices' does not exist")
                
        cursor.close()
        connection.close()
        return True
        
    except Error as e:
        print(f"❌ Database connection failed: {e}")
        return False

if __name__ == "__main__":
    test_database_connection()
