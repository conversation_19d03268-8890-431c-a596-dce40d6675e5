#!/usr/bin/env python3
"""
The Gazette UK Insolvency Notices URL Scraper
==============================================

This script scrapes all notice URLs from The Gazette UK insolvency notices pages.
It uses FlareSolver to bypass Cloudflare protection and extracts URLs from all 32,036 pages.

Features:
- Uses FlareSolver (Docker) to bypass Cloudflare
- Extracts notice URLs from all pages (1 to 32,036)
- Creates two output formats:
  1. Simple text file with all URLs
  2. Organized folder structure with subfolders for every 100 pages
- Progress tracking and error handling
- Resume capability for interrupted runs

Requirements:
- FlareSolver running in Docker container
- Python packages: requests, beautifulsoup4, lxml
"""

import requests
import json
import os
import time
import re
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import logging
from typing import List, Set
import sys
import subprocess
import datetime

# Configuration
FLARESOLVERR_URL = "http://localhost:8191/v1"
BASE_URL = "https://www.thegazette.co.uk/insolvency/notice"
TOTAL_PAGES = 20292  # Updated to match the new URL (Corporate Insolvency only)
TOTAL_NOTICES = 2029200  # Updated total notices count
RESULTS_PER_PAGE = 100
PAGES_PER_FOLDER = 100

# Container refresh settings
CONTAINER_REFRESH_HOURS = 2  # Refresh FlareSolver container every 2 hours
CONTAINER_NAME = "flaresolverr"

# Output paths
SIMPLE_OUTPUT_FILE = "all_gazette_urls.txt"
ORGANIZED_OUTPUT_DIR = "gazette_urls_organized"

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('gazette_scraper.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class GazetteScraper:
    def __init__(self):
        self.session_id = None
        self.all_urls = set()
        self.processed_pages = set()
        self.failed_pages = set()
        self.container_start_time = None

        # Create output directories
        os.makedirs(ORGANIZED_OUTPUT_DIR, exist_ok=True)

        # Load progress if exists
        self.load_progress()

    def check_docker_available(self):
        """Check if Docker is available"""
        try:
            result = subprocess.run(["docker", "--version"], capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except Exception:
            return False

    def stop_flaresolverr_container(self):
        """Stop and remove existing FlareSolver container"""
        try:
            logger.info("Stopping existing FlareSolver container...")

            # Stop container
            subprocess.run(["docker", "stop", CONTAINER_NAME],
                         capture_output=True, text=True, timeout=30)

            # Remove container
            subprocess.run(["docker", "rm", CONTAINER_NAME],
                         capture_output=True, text=True, timeout=30)

            logger.info("✓ FlareSolver container stopped and removed")
            return True
        except Exception as e:
            logger.warning(f"Error stopping container (may not exist): {e}")
            return True  # Continue anyway

    def start_flaresolverr_container(self):
        """Start a new FlareSolver container"""
        try:
            logger.info("Starting new FlareSolver container...")

            cmd = [
                "docker", "run", "-d",
                "--name", CONTAINER_NAME,
                "-p", "8191:8191",
                "ghcr.io/flaresolverr/flaresolverr:latest"
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

            if result.returncode == 0:
                logger.info("✓ FlareSolver container started successfully")

                # Wait for container to be ready
                logger.info("Waiting for FlareSolver to be ready...")
                for i in range(30):
                    try:
                        response = requests.get(FLARESOLVERR_URL.replace('/v1', ''), timeout=5)
                        if response.status_code == 200:
                            logger.info("✓ FlareSolver is ready")
                            self.container_start_time = time.time()
                            return True
                    except:
                        pass
                    time.sleep(2)

                logger.error("FlareSolver container started but not responding")
                return False
            else:
                logger.error(f"Failed to start FlareSolver container: {result.stderr}")
                return False
        except Exception as e:
            logger.error(f"Error starting FlareSolver container: {e}")
            return False

    def refresh_flaresolverr_container(self):
        """Refresh the FlareSolver container (stop old, start new)"""
        logger.info("🔄 Refreshing FlareSolver container...")

        # Destroy current session first
        self.destroy_session()

        # Stop and start container
        self.stop_flaresolverr_container()
        time.sleep(5)  # Wait a bit between stop and start

        if self.start_flaresolverr_container():
            # Create new session
            if self.create_session():
                logger.info("✅ FlareSolver container refreshed successfully")
                return True
            else:
                logger.error("Failed to create session after container refresh")
                return False
        else:
            logger.error("Failed to start new FlareSolver container")
            return False

    def should_refresh_container(self):
        """Check if container should be refreshed based on time"""
        if self.container_start_time is None:
            return False

        elapsed_hours = (time.time() - self.container_start_time) / 3600
        return elapsed_hours >= CONTAINER_REFRESH_HOURS

    def ensure_flaresolverr_running(self):
        """Ensure FlareSolver is running, start if needed"""
        try:
            response = requests.get(FLARESOLVERR_URL.replace('/v1', ''), timeout=10)
            if response.status_code == 200:
                if self.container_start_time is None:
                    self.container_start_time = time.time()
                return True
        except:
            pass

        logger.info("FlareSolver not responding, starting container...")
        return self.start_flaresolverr_container()

    def create_session(self):
        """Create a new FlareSolver session"""
        try:
            payload = {
                "cmd": "sessions.create",
                "session": f"gazette_session_{int(time.time())}"
            }
            response = requests.post(FLARESOLVERR_URL, json=payload, timeout=30)
            response.raise_for_status()

            result = response.json()
            if result.get("status") == "ok":
                self.session_id = result["session"]
                logger.info(f"Created FlareSolver session: {self.session_id}")
                return True
            else:
                logger.error(f"Failed to create session: {result}")
                return False
        except Exception as e:
            logger.error(f"Error creating FlareSolver session: {e}")
            return False

    def destroy_session(self):
        """Destroy the FlareSolver session"""
        if self.session_id:
            try:
                payload = {
                    "cmd": "sessions.destroy",
                    "session": self.session_id
                }
                requests.post(FLARESOLVERR_URL, json=payload, timeout=10)
                logger.info(f"Destroyed session: {self.session_id}")
            except Exception as e:
                logger.warning(f"Error destroying session: {e}")

    def get_page_content(self, page_num: int) -> str:
        """Get page content using FlareSolver"""
        # Updated URL to match user's requirements: Corporate Insolvency only, sorted by oldest date
        url = f"{BASE_URL}?text=&insolvency_corporate=G205010000&insolvency_personal=&location-postcode-1=&location-distance-1=1&location-local-authority-1=&numberOfLocationSearches=1&start-publish-date=&end-publish-date=&edition=&london-issue=&edinburgh-issue=&belfast-issue=&sort-by=oldest-date&results-page-size=100&results-page={page_num}"

        payload = {
            "cmd": "request.get",
            "url": url,
            "session": self.session_id,
            "maxTimeout": 60000
        }

        try:
            response = requests.post(FLARESOLVERR_URL, json=payload, timeout=70)
            response.raise_for_status()

            result = response.json()
            if result.get("status") == "ok":
                return result["solution"]["response"]
            else:
                logger.error(f"FlareSolver error for page {page_num}: {result}")
                return None
        except Exception as e:
            logger.error(f"Error fetching page {page_num}: {e}")
            return None

    def extract_notice_urls(self, html_content: str) -> List[str]:
        """Extract notice URLs from HTML content"""
        try:
            # Try lxml first, fall back to html.parser if not available
            try:
                soup = BeautifulSoup(html_content, 'lxml')
            except:
                soup = BeautifulSoup(html_content, 'html.parser')

            urls = []

            # Find all links that match the notice pattern
            # Notice URLs are in format: /notice/XXXXXXX
            notice_links = soup.find_all('a', href=re.compile(r'^/notice/[^/]+$'))

            for link in notice_links:
                href = link.get('href')
                if href and href.startswith('/notice/'):
                    full_url = urljoin("https://www.thegazette.co.uk", href)
                    urls.append(full_url)

            # Remove duplicates while preserving order
            seen = set()
            unique_urls = []
            for url in urls:
                if url not in seen:
                    seen.add(url)
                    unique_urls.append(url)

            return unique_urls
        except Exception as e:
            logger.error(f"Error extracting URLs from HTML: {e}")
            return []

    def save_progress(self, force_flush=False):
        """Save current progress to file"""
        progress_data = {
            "processed_pages": list(self.processed_pages),
            "failed_pages": list(self.failed_pages),
            "total_urls_found": len(self.all_urls),
            "last_updated": time.time(),
            "container_start_time": self.container_start_time
        }

        try:
            # Write to temporary file first, then rename for atomic operation
            temp_file = "scraper_progress.json.tmp"
            with open(temp_file, "w") as f:
                json.dump(progress_data, f, indent=2)
                if force_flush:
                    f.flush()
                    os.fsync(f.fileno())  # Force write to disk

            # Atomic rename
            os.replace(temp_file, "scraper_progress.json")
        except Exception as e:
            logger.error(f"Error saving progress: {e}")

    def load_progress(self):
        """Load previous progress if exists"""
        try:
            if os.path.exists("scraper_progress.json"):
                with open("scraper_progress.json", "r") as f:
                    progress_data = json.load(f)

                self.processed_pages = set(progress_data.get("processed_pages", []))
                self.failed_pages = set(progress_data.get("failed_pages", []))
                self.container_start_time = progress_data.get("container_start_time")

                logger.info(f"Loaded progress: {len(self.processed_pages)} pages processed, {len(self.failed_pages)} failed")

                if self.processed_pages:
                    max_page = max(self.processed_pages)
                    logger.info(f"Last processed page: {max_page}")

                # Load existing URLs from simple output file
                if os.path.exists(SIMPLE_OUTPUT_FILE):
                    with open(SIMPLE_OUTPUT_FILE, "r") as f:
                        for line in f:
                            url = line.strip()
                            if url:
                                self.all_urls.add(url)
                    logger.info(f"Loaded {len(self.all_urls)} existing URLs")
        except Exception as e:
            logger.error(f"Error loading progress: {e}")

    def get_next_page_to_process(self, start_page: int = 1, end_page: int = None) -> int:
        """Get the next page that needs to be processed"""
        if end_page is None:
            end_page = TOTAL_PAGES

        for page_num in range(start_page, end_page + 1):
            if page_num not in self.processed_pages:
                return page_num

        return end_page + 1  # All pages processed

    def get_folder_name(self, page_num: int) -> str:
        """Get folder name for a given page number"""
        start_page = ((page_num - 1) // PAGES_PER_FOLDER) * PAGES_PER_FOLDER + 1
        end_page = min(start_page + PAGES_PER_FOLDER - 1, TOTAL_PAGES)
        return f"pages_{start_page}-{end_page}"

    def save_urls_to_organized_structure(self, page_num: int, urls: List[str]):
        """Save URLs to organized folder structure"""
        if not urls:
            return

        folder_name = self.get_folder_name(page_num)
        folder_path = os.path.join(ORGANIZED_OUTPUT_DIR, folder_name)
        os.makedirs(folder_path, exist_ok=True)

        file_path = os.path.join(folder_path, f"page_{page_num}_urls.txt")

        try:
            with open(file_path, "w") as f:
                for url in urls:
                    f.write(f"{url}\n")
        except Exception as e:
            logger.error(f"Error saving organized URLs for page {page_num}: {e}")

    def append_urls_to_simple_file(self, urls: List[str]):
        """Append URLs to simple output file"""
        if not urls:
            return

        try:
            with open(SIMPLE_OUTPUT_FILE, "a") as f:
                for url in urls:
                    if url not in self.all_urls:
                        f.write(f"{url}\n")
                        self.all_urls.add(url)
        except Exception as e:
            logger.error(f"Error appending URLs to simple file: {e}")

    def process_page(self, page_num: int) -> bool:
        """Process a single page and extract URLs"""
        if page_num in self.processed_pages:
            logger.info(f"Page {page_num} already processed, skipping")
            return True

        logger.info(f"Processing page {page_num}/{TOTAL_PAGES}")

        # Get page content
        html_content = self.get_page_content(page_num)
        if not html_content:
            logger.error(f"Failed to get content for page {page_num}")
            self.failed_pages.add(page_num)
            return False

        # Extract URLs
        urls = self.extract_notice_urls(html_content)
        if not urls:
            logger.warning(f"No URLs found on page {page_num}")
        else:
            logger.info(f"Found {len(urls)} URLs on page {page_num}")

        # Save URLs
        self.save_urls_to_organized_structure(page_num, urls)
        self.append_urls_to_simple_file(urls)

        # Mark as processed and save progress immediately
        self.processed_pages.add(page_num)
        self.save_progress(force_flush=True)  # Save after each page for better resume

        return True

    def run_scraper(self, start_page: int = 1, end_page: int = None):
        """Run the main scraping process"""
        if end_page is None:
            end_page = TOTAL_PAGES

        logger.info(f"Starting Gazette scraper for pages {start_page} to {end_page}")
        logger.info(f"Total pages to process: {end_page - start_page + 1}")
        logger.info(f"Container refresh interval: {CONTAINER_REFRESH_HOURS} hours")

        # Ensure FlareSolver is running
        if not self.ensure_flaresolverr_running():
            logger.error("Failed to start FlareSolver container. Exiting.")
            return False

        # Create FlareSolver session
        if not self.create_session():
            logger.error("Failed to create FlareSolver session. Exiting.")
            return False

        try:
            processed_count = 0
            failed_count = 0
            last_container_refresh = time.time()

            for page_num in range(start_page, end_page + 1):
                # Skip if already processed
                if page_num in self.processed_pages:
                    continue

                # Check if container needs refresh (every 2 hours)
                if self.should_refresh_container():
                    logger.info(f"🔄 Container has been running for {CONTAINER_REFRESH_HOURS}+ hours, refreshing...")
                    if self.refresh_flaresolverr_container():
                        logger.info("✅ Container refreshed successfully, continuing...")
                        last_container_refresh = time.time()
                    else:
                        logger.error("❌ Failed to refresh container, continuing with current one...")

                success = self.process_page(page_num)

                if success:
                    processed_count += 1
                    failed_count = 0  # Reset failed count on success
                else:
                    failed_count += 1

                # Log progress every 10 pages (but progress is saved after each page)
                if processed_count % 10 == 0:
                    logger.info(f"Progress: {len(self.processed_pages)}/{TOTAL_PAGES} pages processed, {len(self.all_urls)} total URLs found")

                # Add delay to avoid overwhelming the server
                time.sleep(1)

                # Handle rate limiting - increase delay if we get failures
                if failed_count > 5:
                    logger.warning("Multiple failures detected, increasing delay and refreshing container...")
                    time.sleep(5)
                    # Try refreshing container if too many failures
                    if self.refresh_flaresolverr_container():
                        logger.info("Container refreshed due to failures")
                        failed_count = 0
                    else:
                        logger.error("Failed to refresh container")
                        failed_count = 0  # Reset anyway to avoid infinite loop

            # Final save
            self.save_progress()

            logger.info(f"Scraping completed!")
            logger.info(f"Total pages processed: {len(self.processed_pages)}")
            logger.info(f"Total URLs found: {len(self.all_urls)}")
            logger.info(f"Failed pages: {len(self.failed_pages)}")

            if self.failed_pages:
                logger.warning(f"Failed pages: {sorted(list(self.failed_pages))}")

            return True

        except KeyboardInterrupt:
            logger.info("Scraping interrupted by user")
            self.save_progress()
            return False
        except Exception as e:
            logger.error(f"Unexpected error during scraping: {e}")
            self.save_progress()
            return False
        finally:
            self.destroy_session()

    def retry_failed_pages(self):
        """Retry processing failed pages"""
        if not self.failed_pages:
            logger.info("No failed pages to retry")
            return

        logger.info(f"Retrying {len(self.failed_pages)} failed pages")
        failed_pages_copy = self.failed_pages.copy()
        self.failed_pages.clear()

        for page_num in sorted(failed_pages_copy):
            logger.info(f"Retrying page {page_num}")
            success = self.process_page(page_num)
            if not success:
                self.failed_pages.add(page_num)
            time.sleep(2)  # Longer delay for retries

    def generate_summary_report(self):
        """Generate a summary report of the scraping results"""
        report_path = "scraping_summary_report.txt"

        try:
            with open(report_path, "w") as f:
                f.write("The Gazette UK Insolvency Notices Scraping Summary\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"Total pages in website: {TOTAL_PAGES:,}\n")
                f.write(f"Pages successfully processed: {len(self.processed_pages):,}\n")
                f.write(f"Pages failed: {len(self.failed_pages):,}\n")
                f.write(f"Total unique URLs extracted: {len(self.all_urls):,}\n")
                f.write(f"Expected URLs (approx): {TOTAL_PAGES * RESULTS_PER_PAGE:,}\n\n")

                f.write("Output Files:\n")
                f.write(f"- Simple text file: {SIMPLE_OUTPUT_FILE}\n")
                f.write(f"- Organized folders: {ORGANIZED_OUTPUT_DIR}/\n\n")

                if self.failed_pages:
                    f.write("Failed Pages:\n")
                    failed_list = sorted(list(self.failed_pages))
                    for i in range(0, len(failed_list), 10):
                        batch = failed_list[i:i+10]
                        f.write(f"  {', '.join(map(str, batch))}\n")

                f.write(f"\nScraping completed at: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")

            logger.info(f"Summary report saved to: {report_path}")
        except Exception as e:
            logger.error(f"Error generating summary report: {e}")


def main():
    """Main function"""
    print("The Gazette UK Insolvency Notices URL Scraper")
    print("=" * 50)
    print("🎯 Target: Corporate Insolvency Notices Only (sorted by oldest date)")
    print(f"📊 Total pages: {TOTAL_PAGES:,}")
    print(f"📋 Expected URLs: ~{TOTAL_NOTICES:,}")
    print(f"🔧 FlareSolver URL: {FLARESOLVERR_URL}")
    print()

    # Check if FlareSolver is running
    try:
        response = requests.get(f"{FLARESOLVERR_URL.replace('/v1', '')}", timeout=5)
        if response.status_code != 200:
            print("ERROR: FlareSolver is not running or not accessible!")
            print("Please start FlareSolver with: docker run -d --name=flaresolverr -p 8191:8191 ghcr.io/flaresolverr/flaresolverr:latest")
            return
    except Exception as e:
        print(f"ERROR: Cannot connect to FlareSolver: {e}")
        print("Please start FlareSolver with: docker run -d --name=flaresolverr -p 8191:8191 ghcr.io/flaresolverr/flaresolverr:latest")
        return

    scraper = GazetteScraper()

    # Ask user for options
    print("Options:")
    print("1. Start fresh scraping (from page 1)")
    print("2. Resume previous scraping")
    print("3. Retry failed pages only")
    print("4. Custom page range")

    choice = input("Enter your choice (1-4): ").strip()

    if choice == "1":
        # Clear previous progress
        if os.path.exists("scraper_progress.json"):
            os.remove("scraper_progress.json")
        if os.path.exists(SIMPLE_OUTPUT_FILE):
            os.remove(SIMPLE_OUTPUT_FILE)
        scraper = GazetteScraper()  # Reinitialize
        scraper.run_scraper()
    elif choice == "2":
        # Resume scraping - show current progress
        next_page = scraper.get_next_page_to_process()
        if next_page <= TOTAL_PAGES:
            print(f"Resuming from page {next_page}")
            print(f"Progress: {len(scraper.processed_pages)}/{TOTAL_PAGES} pages completed")
            print(f"URLs found so far: {len(scraper.all_urls)}")
        else:
            print("All pages have been processed!")
            return
        scraper.run_scraper()
    elif choice == "3":
        if not scraper.create_session():
            print("Failed to create FlareSolver session")
            return
        scraper.retry_failed_pages()
        scraper.destroy_session()
    elif choice == "4":
        try:
            start = int(input("Enter start page: "))
            end = int(input("Enter end page: "))
            if 1 <= start <= end <= TOTAL_PAGES:
                scraper.run_scraper(start, end)
            else:
                print(f"Invalid range. Pages must be between 1 and {TOTAL_PAGES}")
        except ValueError:
            print("Invalid input. Please enter numbers only.")
    else:
        print("Invalid choice")
        return

    # Generate summary report
    scraper.generate_summary_report()

    print("\nScraping completed! Check the output files:")
    print(f"- Simple text file: {SIMPLE_OUTPUT_FILE}")
    print(f"- Organized folders: {ORGANIZED_OUTPUT_DIR}/")
    print("- Summary report: scraping_summary_report.txt")


if __name__ == "__main__":
    main()
