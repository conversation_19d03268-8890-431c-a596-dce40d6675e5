#!/usr/bin/env python3
"""
Test script to isolate import issues
"""

print("Testing imports...")

try:
    print("1. Testing basic imports...")
    import os
    import sys
    import time
    import random
    import signal
    import threading
    print("   Basic imports OK")
    
    print("2. Testing requests...")
    import requests
    print("   Requests OK")
    
    print("3. Testing BeautifulSoup...")
    from bs4 import BeautifulSoup
    print("   BeautifulSoup OK")
    
    print("4. Testing MySQL connector...")
    import mysql.connector
    print("   MySQL connector OK")
    
    print("5. Testing concurrent.futures...")
    from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
    print("   Concurrent futures OK")
    
    print("6. Testing subprocess...")
    import subprocess
    print("   Subprocess OK")
    
    print("All imports successful!")
    
except Exception as e:
    print(f"Import failed: {e}")
    import traceback
    traceback.print_exc()
