#!/usr/bin/env python3
"""
CPU-Parallel Gazette Scraper using multiprocessing
==================================================

This version demonstrates CPU-based parallelization for HTML parsing:
1. Separate processes for HTML parsing to utilize multiple CPU cores
2. Shared memory for efficient data transfer
3. Process pool for parsing while main process handles I/O
"""

import multiprocessing as mp
import requests
import json
import os
import time
import re
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import logging
from typing import List, Set, Dict, Optional, Tuple
import sys
from functools import partial

# Configuration (same as original)
FLARESOLVERR_URL = "http://localhost:8191/v1"
BASE_URL = "https://www.thegazette.co.uk/insolvency/notice"
TOTAL_PAGES = 20292
RESULTS_PER_PAGE = 100
PAGES_PER_FOLDER = 100

# Multiprocessing settings
CPU_CORES = mp.cpu_count()
PARSING_PROCESSES = max(2, CPU_CORES - 1)  # Leave one core for I/O

# Output paths
SIMPLE_OUTPUT_FILE = "all_gazette_urls_multiprocess.txt"
ORGANIZED_OUTPUT_DIR = "gazette_urls_organized_multiprocess"

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('gazette_scraper_multiprocess.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def extract_notice_urls_worker(html_content: str) -> List[str]:
    """
    Worker function for extracting URLs - designed to run in separate process
    This function is isolated and doesn't depend on class state
    """
    try:
        # Try lxml first, fall back to html.parser if not available
        try:
            soup = BeautifulSoup(html_content, 'lxml')
        except:
            soup = BeautifulSoup(html_content, 'html.parser')

        urls = []
        
        # Find all links that match the notice pattern
        notice_links = soup.find_all('a', href=re.compile(r'^/notice/[^/]+$'))
        
        for link in notice_links:
            href = link.get('href')
            if href and href.startswith('/notice/'):
                full_url = urljoin("https://www.thegazette.co.uk", href)
                urls.append(full_url)

        # Remove duplicates while preserving order
        seen = set()
        unique_urls = []
        for url in urls:
            if url not in seen:
                seen.add(url)
                unique_urls.append(url)

        return unique_urls
    except Exception as e:
        logger.error(f"Error extracting URLs from HTML: {e}")
        return []

def process_page_batch(page_data_batch: List[Tuple[int, str]]) -> List[Tuple[int, List[str], bool]]:
    """
    Process a batch of pages in a single worker process
    Returns list of (page_num, urls, success) tuples
    """
    results = []
    for page_num, html_content in page_data_batch:
        try:
            start_time = time.time()
            urls = extract_notice_urls_worker(html_content)
            parse_time = time.time() - start_time
            
            results.append((page_num, urls, True, parse_time))
            logger.info(f"Worker processed page {page_num}: {len(urls)} URLs in {parse_time:.3f}s")
        except Exception as e:
            logger.error(f"Worker failed to process page {page_num}: {e}")
            results.append((page_num, [], False, 0))
    
    return results

class MultiprocessGazetteScraper:
    def __init__(self):
        self.session_id = None
        self.all_urls = set()
        self.processed_pages = set()
        self.failed_pages = set()
        
        # Create output directories
        os.makedirs(ORGANIZED_OUTPUT_DIR, exist_ok=True)
        
        # Load progress if exists
        self.load_progress()

    def create_session(self) -> bool:
        """Create a new FlareSolver session (same as original)"""
        try:
            payload = {
                "cmd": "sessions.create",
                "session": f"gazette_session_{int(time.time())}"
            }
            response = requests.post(FLARESOLVERR_URL, json=payload, timeout=30)
            response.raise_for_status()

            result = response.json()
            if result.get("status") == "ok":
                self.session_id = result["session"]
                logger.info(f"Created FlareSolver session: {self.session_id}")
                return True
            else:
                logger.error(f"Failed to create session: {result}")
                return False
        except Exception as e:
            logger.error(f"Error creating FlareSolver session: {e}")
            return False

    def get_page_content(self, page_num: int) -> Optional[str]:
        """Get page content using FlareSolver (same as original)"""
        url = f"{BASE_URL}?text=&insolvency_corporate=G205010000&insolvency_personal=&location-postcode-1=&location-distance-1=1&location-local-authority-1=&numberOfLocationSearches=1&start-publish-date=&end-publish-date=&edition=&london-issue=&edinburgh-issue=&belfast-issue=&sort-by=oldest-date&results-page-size=100&results-page={page_num}"

        payload = {
            "cmd": "request.get",
            "url": url,
            "session": self.session_id,
            "maxTimeout": 60000
        }

        try:
            response = requests.post(FLARESOLVERR_URL, json=payload, timeout=70)
            response.raise_for_status()

            result = response.json()
            if result.get("status") == "ok":
                return result["solution"]["response"]
            else:
                logger.error(f"FlareSolver error for page {page_num}: {result}")
                return None
        except Exception as e:
            logger.error(f"Error fetching page {page_num}: {e}")
            return None

    def fetch_page_batch(self, page_numbers: List[int]) -> List[Tuple[int, str]]:
        """Fetch a batch of pages sequentially"""
        page_data = []
        for page_num in page_numbers:
            if page_num in self.processed_pages:
                continue
                
            logger.info(f"Fetching page {page_num}")
            html_content = self.get_page_content(page_num)
            
            if html_content:
                page_data.append((page_num, html_content))
            else:
                self.failed_pages.add(page_num)
                logger.error(f"Failed to fetch page {page_num}")
            
            # Small delay to avoid overwhelming the server
            time.sleep(0.5)
        
        return page_data

    def save_urls_to_organized_structure(self, page_num: int, urls: List[str]):
        """Save URLs to organized folder structure (same as original)"""
        if not urls:
            return

        folder_name = self.get_folder_name(page_num)
        folder_path = os.path.join(ORGANIZED_OUTPUT_DIR, folder_name)
        os.makedirs(folder_path, exist_ok=True)

        file_path = os.path.join(folder_path, f"page_{page_num}_urls.txt")

        try:
            with open(file_path, "w") as f:
                for url in urls:
                    f.write(f"{url}\n")
        except Exception as e:
            logger.error(f"Error saving organized URLs for page {page_num}: {e}")

    def append_urls_to_simple_file(self, urls: List[str]):
        """Append URLs to simple output file (same as original)"""
        if not urls:
            return

        try:
            with open(SIMPLE_OUTPUT_FILE, "a") as f:
                for url in urls:
                    if url not in self.all_urls:
                        f.write(f"{url}\n")
                        self.all_urls.add(url)
        except Exception as e:
            logger.error(f"Error appending URLs to simple file: {e}")

    def get_folder_name(self, page_num: int) -> str:
        """Get folder name for a given page number (same as original)"""
        start_page = ((page_num - 1) // PAGES_PER_FOLDER) * PAGES_PER_FOLDER + 1
        end_page = min(start_page + PAGES_PER_FOLDER - 1, TOTAL_PAGES)
        return f"pages_{start_page}-{end_page}"

    def save_progress(self):
        """Save current progress to file (same as original)"""
        progress_data = {
            "processed_pages": list(self.processed_pages),
            "failed_pages": list(self.failed_pages),
            "total_urls_found": len(self.all_urls),
            "last_updated": time.time()
        }

        try:
            with open("scraper_progress_multiprocess.json", "w") as f:
                json.dump(progress_data, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving progress: {e}")

    def load_progress(self):
        """Load previous progress if exists (same as original)"""
        try:
            if os.path.exists("scraper_progress_multiprocess.json"):
                with open("scraper_progress_multiprocess.json", "r") as f:
                    progress_data = json.load(f)

                self.processed_pages = set(progress_data.get("processed_pages", []))
                self.failed_pages = set(progress_data.get("failed_pages", []))

                logger.info(f"Loaded progress: {len(self.processed_pages)} pages processed")

                # Load existing URLs
                if os.path.exists(SIMPLE_OUTPUT_FILE):
                    with open(SIMPLE_OUTPUT_FILE, "r") as f:
                        for line in f:
                            url = line.strip()
                            if url:
                                self.all_urls.add(url)
                    logger.info(f"Loaded {len(self.all_urls)} existing URLs")
        except Exception as e:
            logger.error(f"Error loading progress: {e}")

    def run_multiprocess_scraper(self, start_page: int = 1, end_page: int = None, batch_size: int = 10):
        """
        Run the multiprocess scraping process
        
        Args:
            start_page: Starting page number
            end_page: Ending page number
            batch_size: Number of pages to fetch before sending to processing pool
        """
        if end_page is None:
            end_page = TOTAL_PAGES

        logger.info(f"Starting multiprocess Gazette scraper for pages {start_page} to {end_page}")
        logger.info(f"Using {PARSING_PROCESSES} processes for HTML parsing")
        logger.info(f"Batch size: {batch_size} pages")

        # Create FlareSolver session
        if not self.create_session():
            logger.error("Failed to create FlareSolver session")
            return False

        try:
            with mp.Pool(processes=PARSING_PROCESSES) as pool:
                total_pages = end_page - start_page + 1
                processed_count = 0
                
                # Process pages in batches
                for batch_start in range(start_page, end_page + 1, batch_size):
                    batch_end = min(batch_start + batch_size - 1, end_page)
                    page_numbers = list(range(batch_start, batch_end + 1))
                    
                    logger.info(f"Processing batch: pages {batch_start}-{batch_end}")
                    
                    # Fetch pages sequentially (I/O bound)
                    fetch_start = time.time()
                    page_data = self.fetch_page_batch(page_numbers)
                    fetch_time = time.time() - fetch_start
                    
                    if not page_data:
                        logger.warning(f"No data fetched for batch {batch_start}-{batch_end}")
                        continue
                    
                    logger.info(f"Fetched {len(page_data)} pages in {fetch_time:.2f}s")
                    
                    # Split page data into chunks for parallel processing
                    chunk_size = max(1, len(page_data) // PARSING_PROCESSES)
                    page_chunks = [page_data[i:i + chunk_size] for i in range(0, len(page_data), chunk_size)]
                    
                    # Process chunks in parallel
                    parse_start = time.time()
                    chunk_results = pool.map(process_page_batch, page_chunks)
                    parse_time = time.time() - parse_start
                    
                    logger.info(f"Parsed {len(page_data)} pages in {parse_time:.2f}s using {len(page_chunks)} processes")
                    
                    # Collect and save results
                    for chunk_result in chunk_results:
                        for page_num, urls, success, individual_parse_time in chunk_result:
                            if success:
                                # Save URLs (same logic as original)
                                self.save_urls_to_organized_structure(page_num, urls)
                                self.append_urls_to_simple_file(urls)
                                
                                # Mark as processed
                                self.processed_pages.add(page_num)
                                processed_count += 1
                                
                                logger.info(f"Saved page {page_num}: {len(urls)} URLs")
                            else:
                                self.failed_pages.add(page_num)
                                logger.error(f"Failed to process page {page_num}")
                    
                    # Save progress after each batch
                    self.save_progress()
                    
                    logger.info(f"Batch progress: {processed_count}/{total_pages} pages processed")
                    logger.info(f"Total URLs found so far: {len(self.all_urls)}")

            # Final save
            self.save_progress()
            
            logger.info(f"Multiprocess scraping completed!")
            logger.info(f"Total pages processed: {len(self.processed_pages)}")
            logger.info(f"Total URLs found: {len(self.all_urls)}")
            logger.info(f"Failed pages: {len(self.failed_pages)}")
            
            return True

        except Exception as e:
            logger.error(f"Error during multiprocess scraping: {e}")
            return False

# Example usage
def main():
    scraper = MultiprocessGazetteScraper()
    scraper.run_multiprocess_scraper(1, 50, batch_size=5)  # Test with first 50 pages

if __name__ == "__main__":
    # Required for Windows multiprocessing
    mp.freeze_support()
    main()
