#!/usr/bin/env python3
"""
Performance Benchmark for Different Scraping Approaches
=======================================================

This script benchmarks different optimization strategies:
1. Original sequential approach
2. Async I/O with threaded parsing
3. Multiprocess CPU parallelization
4. Hybrid approach combining async + multiprocessing
"""

import time
import asyncio
import multiprocessing as mp
import statistics
from typing import List, Dict, Any
import logging
import json
from dataclasses import dataclass

# Import our different scraper implementations
from gazette import GazetteScraper as OriginalScraper
from gazette_async_optimized import OptimizedGazetteScraper as AsyncScraper
from gazette_multiprocess_optimized import MultiprocessGazetteScraper as MultiprocessScraper

@dataclass
class BenchmarkResult:
    """Container for benchmark results"""
    approach: str
    pages_processed: int
    total_time: float
    pages_per_second: float
    urls_found: int
    failed_pages: int
    cpu_utilization: float
    memory_usage_mb: float

class PerformanceBenchmark:
    def __init__(self, test_pages: int = 20):
        """
        Initialize benchmark
        
        Args:
            test_pages: Number of pages to test with each approach
        """
        self.test_pages = test_pages
        self.results: List[BenchmarkResult] = []
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('benchmark.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def benchmark_original_approach(self) -> BenchmarkResult:
        """Benchmark the original sequential approach"""
        self.logger.info("Benchmarking original sequential approach...")
        
        start_time = time.time()
        scraper = OriginalScraper()
        
        # Ensure FlareSolver is running
        if not scraper.ensure_flaresolverr_running():
            raise Exception("FlareSolver not available")
        
        if not scraper.create_session():
            raise Exception("Failed to create session")
        
        try:
            success = scraper.run_scraper(1, self.test_pages)
            end_time = time.time()
            
            total_time = end_time - start_time
            
            return BenchmarkResult(
                approach="Original Sequential",
                pages_processed=len(scraper.processed_pages),
                total_time=total_time,
                pages_per_second=len(scraper.processed_pages) / total_time if total_time > 0 else 0,
                urls_found=len(scraper.all_urls),
                failed_pages=len(scraper.failed_pages),
                cpu_utilization=0,  # Would need psutil for accurate measurement
                memory_usage_mb=0   # Would need psutil for accurate measurement
            )
        finally:
            scraper.destroy_session()

    async def benchmark_async_approach(self) -> BenchmarkResult:
        """Benchmark the async I/O approach"""
        self.logger.info("Benchmarking async I/O approach...")
        
        start_time = time.time()
        scraper = AsyncScraper()
        
        try:
            success = await scraper.run_optimized_scraper(1, self.test_pages)
            end_time = time.time()
            
            total_time = end_time - start_time
            
            return BenchmarkResult(
                approach="Async I/O + Threading",
                pages_processed=len(scraper.processed_pages),
                total_time=total_time,
                pages_per_second=len(scraper.processed_pages) / total_time if total_time > 0 else 0,
                urls_found=len(scraper.all_urls),
                failed_pages=len(scraper.failed_pages),
                cpu_utilization=0,
                memory_usage_mb=0
            )
        except Exception as e:
            self.logger.error(f"Async benchmark failed: {e}")
            raise

    def benchmark_multiprocess_approach(self) -> BenchmarkResult:
        """Benchmark the multiprocess approach"""
        self.logger.info("Benchmarking multiprocess approach...")
        
        start_time = time.time()
        scraper = MultiprocessScraper()
        
        try:
            success = scraper.run_multiprocess_scraper(1, self.test_pages, batch_size=5)
            end_time = time.time()
            
            total_time = end_time - start_time
            
            return BenchmarkResult(
                approach="Multiprocess CPU",
                pages_processed=len(scraper.processed_pages),
                total_time=total_time,
                pages_per_second=len(scraper.processed_pages) / total_time if total_time > 0 else 0,
                urls_found=len(scraper.all_urls),
                failed_pages=len(scraper.failed_pages),
                cpu_utilization=0,
                memory_usage_mb=0
            )
        except Exception as e:
            self.logger.error(f"Multiprocess benchmark failed: {e}")
            raise

    def run_benchmark_suite(self) -> List[BenchmarkResult]:
        """Run all benchmarks and return results"""
        self.logger.info(f"Starting benchmark suite with {self.test_pages} pages")
        
        results = []
        
        try:
            # Benchmark 1: Original approach
            result1 = self.benchmark_original_approach()
            results.append(result1)
            self.logger.info(f"Original: {result1.pages_per_second:.2f} pages/sec")
            
            # Small delay between tests
            time.sleep(5)
            
            # Benchmark 2: Async approach
            result2 = asyncio.run(self.benchmark_async_approach())
            results.append(result2)
            self.logger.info(f"Async: {result2.pages_per_second:.2f} pages/sec")
            
            # Small delay between tests
            time.sleep(5)
            
            # Benchmark 3: Multiprocess approach
            result3 = self.benchmark_multiprocess_approach()
            results.append(result3)
            self.logger.info(f"Multiprocess: {result3.pages_per_second:.2f} pages/sec")
            
        except Exception as e:
            self.logger.error(f"Benchmark suite failed: {e}")
            raise
        
        self.results = results
        return results

    def generate_report(self) -> str:
        """Generate a detailed performance report"""
        if not self.results:
            return "No benchmark results available"
        
        report = []
        report.append("Performance Benchmark Report")
        report.append("=" * 50)
        report.append(f"Test Configuration: {self.test_pages} pages per approach")
        report.append("")
        
        # Sort results by pages per second (descending)
        sorted_results = sorted(self.results, key=lambda x: x.pages_per_second, reverse=True)
        
        report.append("Results (sorted by performance):")
        report.append("-" * 40)
        
        for i, result in enumerate(sorted_results, 1):
            report.append(f"{i}. {result.approach}")
            report.append(f"   Pages processed: {result.pages_processed}")
            report.append(f"   Total time: {result.total_time:.2f} seconds")
            report.append(f"   Performance: {result.pages_per_second:.2f} pages/second")
            report.append(f"   URLs found: {result.urls_found}")
            report.append(f"   Failed pages: {result.failed_pages}")
            
            if i == 1:
                report.append("   🏆 BEST PERFORMANCE")
            
            report.append("")
        
        # Performance comparison
        if len(sorted_results) > 1:
            best = sorted_results[0]
            report.append("Performance Improvements:")
            report.append("-" * 30)
            
            for result in sorted_results[1:]:
                if result.pages_per_second > 0:
                    improvement = (best.pages_per_second / result.pages_per_second - 1) * 100
                    report.append(f"{best.approach} is {improvement:.1f}% faster than {result.approach}")
        
        # Recommendations
        report.append("")
        report.append("Recommendations:")
        report.append("-" * 20)
        
        if sorted_results[0].approach == "Async I/O + Threading":
            report.append("✅ Use async I/O approach for best I/O-bound performance")
            report.append("   - Excellent for concurrent HTTP requests")
            report.append("   - Lower memory overhead than multiprocessing")
            report.append("   - Scales well with network latency")
        elif sorted_results[0].approach == "Multiprocess CPU":
            report.append("✅ Use multiprocess approach for CPU-intensive parsing")
            report.append("   - Best for complex HTML parsing workloads")
            report.append("   - Utilizes multiple CPU cores effectively")
            report.append("   - Good for large HTML documents")
        else:
            report.append("ℹ️  Original approach performed best")
            report.append("   - May indicate network bottleneck")
            report.append("   - Consider optimizing FlareSolver configuration")
        
        return "\n".join(report)

    def save_results(self, filename: str = "benchmark_results.json"):
        """Save benchmark results to JSON file"""
        if not self.results:
            return
        
        data = {
            "test_configuration": {
                "pages_tested": self.test_pages,
                "timestamp": time.time()
            },
            "results": [
                {
                    "approach": r.approach,
                    "pages_processed": r.pages_processed,
                    "total_time": r.total_time,
                    "pages_per_second": r.pages_per_second,
                    "urls_found": r.urls_found,
                    "failed_pages": r.failed_pages
                }
                for r in self.results
            ]
        }
        
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)
        
        self.logger.info(f"Results saved to {filename}")

def main():
    """Run the benchmark suite"""
    print("Performance Benchmark for Web Scraping Optimization")
    print("=" * 55)
    print()
    
    # Ask user for test configuration
    try:
        test_pages = int(input("Enter number of pages to test (default 10): ") or "10")
    except ValueError:
        test_pages = 10
    
    print(f"Running benchmark with {test_pages} pages per approach...")
    print("This may take several minutes...")
    print()
    
    benchmark = PerformanceBenchmark(test_pages)
    
    try:
        results = benchmark.run_benchmark_suite()
        
        # Generate and display report
        report = benchmark.generate_report()
        print(report)
        
        # Save results
        benchmark.save_results()
        
        print("\nBenchmark completed successfully!")
        print("Results saved to benchmark_results.json")
        
    except Exception as e:
        print(f"Benchmark failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    # Required for Windows multiprocessing
    mp.freeze_support()
    exit(main())
