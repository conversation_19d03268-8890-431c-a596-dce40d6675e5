#!/usr/bin/env python3
"""
Enhanced Parallel Gazette Scraper with Concurrent Processing
Optimized for MacBook Pro M1 with 16GB RAM
Maintains all existing functionality while adding parallel processing
"""

import requests
import json
import time
import sys
import argparse
import re
import logging
import mysql.connector
from datetime import datetime
from bs4 import BeautifulSoup
from typing import Dict, List, Optional
from urllib.parse import urljoin
import os
import threading
import concurrent.futures
from queue import Queue, Empty
from dataclasses import dataclass
from contextlib import contextmanager
import random
import subprocess
import signal

# Configuration
FAILED_LOG_FILE = "failed_urls.log"
PROCESSED_URLS_FILE = "processed_urls.txt"

# FlareSolverr Container Management Configuration
FLARESOLVERR_CONFIG = {
    'container_name': 'flaresolverr_gazette_scraper',
    'docker_image': 'ghcr.io/flaresolverr/flaresolverr:latest',
    'port': '8191:8191',
    'restart_interval_hours': 4,  # Restart every 4 hours
    'wait_after_restart_minutes': 4,  # Wait 4 minutes after restart
    'max_restart_attempts': 3,  # Maximum attempts to restart container
}

# MySQL configuration for WAMP Server
MYSQL_DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '',  # Default WAMP password is empty
    'database': 'gazette_scraper',
    'charset': 'utf8mb4',
    'autocommit': False
}

# Parallel processing configuration optimized for 16GB RAM system
# AGGRESSIVE CONFIGURATION: Maximized for single scraper with 16GB RAM
PARALLEL_CONFIG = {
    'max_workers': 12,  # Aggressive worker count for 16GB RAM
    'max_db_connections': 8,  # More DB connections for parallel inserts
    'batch_size': 25,  # Larger batches to utilize more memory efficiently
    'request_delay': 0.05,  # Minimal delay for maximum throughput
    'max_retries': 3,  # Standard retries
    'timeout': 45,  # Reduced timeout for faster failure detection
    'session_pool_size': 4,  # Multiple FlareSolverr sessions for parallel requests
    'concurrent_page_requests': 4,  # Multiple concurrent page requests
    'user_agent_rotation': True,  # Keep user agent rotation
    'random_delays': True,  # Keep random delays
    'connection_reuse': True,  # Reuse connections for efficiency
    'shared_environment': False,  # Dedicated scraper mode
    'memory_optimization': True,  # Enable memory optimization features
    'prefetch_enabled': True,  # Enable URL prefetching
    'cache_size': 1000,  # Cache for processed data
}

# Logging setup
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('gazette_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Failed URLs logger
failed_logger = logging.getLogger('failed_urls')
failed_handler = logging.FileHandler(FAILED_LOG_FILE)
failed_handler.setFormatter(logging.Formatter('%(asctime)s - %(message)s'))
failed_logger.addHandler(failed_handler)
failed_logger.setLevel(logging.INFO)

# User agent rotation for anti-detection
USER_AGENTS = [
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
]

@dataclass
class ProcessingResult:
    """Result of processing a single URL"""
    url: str
    success: bool
    data: Optional[Dict] = None
    error: Optional[str] = None
    url_index: int = 0

class FlareSolverrContainerManager:
    """Manages FlareSolverr Docker container lifecycle"""

    def __init__(self, config: Dict = FLARESOLVERR_CONFIG):
        self.config = config
        self.container_name = config['container_name']
        self.docker_image = config['docker_image']
        self.port = config['port']
        self.restart_interval = config['restart_interval_hours'] * 3600  # Convert to seconds
        self.wait_time = config['wait_after_restart_minutes'] * 60  # Convert to seconds
        self.max_attempts = config['max_restart_attempts']
        self.last_restart_time = time.time()

    def is_container_running(self) -> bool:
        """Check if FlareSolverr container is running"""
        try:
            result = subprocess.run(
                ['docker', 'ps', '--filter', f'name={self.container_name}', '--format', '{{.Names}}'],
                capture_output=True, text=True, timeout=10
            )
            return self.container_name in result.stdout
        except Exception as e:
            logger.warning(f"Error checking container status: {e}")
            return False

    def stop_container(self) -> bool:
        """Stop and remove FlareSolverr container"""
        try:
            logger.info(f"[CONTAINER] Stopping FlareSolverr container: {self.container_name}")

            # Stop container
            subprocess.run(['docker', 'stop', self.container_name],
                         capture_output=True, timeout=30)

            # Remove container
            subprocess.run(['docker', 'rm', self.container_name],
                         capture_output=True, timeout=30)

            logger.info(f"[CONTAINER] Successfully stopped and removed container")
            return True

        except Exception as e:
            logger.error(f"[CONTAINER] Error stopping container: {e}")
            return False

    def start_container(self) -> bool:
        """Start FlareSolverr container"""
        try:
            logger.info(f"[CONTAINER] Starting FlareSolverr container: {self.container_name}")

            # Start new container
            cmd = [
                'docker', 'run', '-d',
                '--name', self.container_name,
                '-p', self.port,
                '--restart', 'unless-stopped',
                self.docker_image
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

            if result.returncode == 0:
                logger.info(f"[CONTAINER] Successfully started container")

                # Wait for container to be ready
                logger.info(f"[CONTAINER] Waiting {self.wait_time//60} minutes for container to be ready...")
                time.sleep(self.wait_time)

                return True
            else:
                logger.error(f"[CONTAINER] Failed to start container: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"[CONTAINER] Error starting container: {e}")
            return False

    def restart_container(self) -> bool:
        """Restart FlareSolverr container"""
        logger.info(f"[CONTAINER] Restarting FlareSolverr container for maintenance...")

        for attempt in range(self.max_attempts):
            logger.info(f"[CONTAINER] Restart attempt {attempt + 1}/{self.max_attempts}")

            # Stop existing container
            if self.is_container_running():
                if not self.stop_container():
                    logger.warning(f"[CONTAINER] Failed to stop container on attempt {attempt + 1}")
                    continue

            # Start new container
            if self.start_container():
                self.last_restart_time = time.time()
                logger.info(f"[CONTAINER] Container restart successful!")
                return True
            else:
                logger.warning(f"[CONTAINER] Failed to start container on attempt {attempt + 1}")
                if attempt < self.max_attempts - 1:
                    logger.info(f"[CONTAINER] Waiting 30 seconds before retry...")
                    time.sleep(30)

        logger.error(f"[CONTAINER] Failed to restart container after {self.max_attempts} attempts")
        return False

    def should_restart(self) -> bool:
        """Check if container should be restarted based on time interval"""
        current_time = time.time()
        time_since_restart = current_time - self.last_restart_time
        return time_since_restart >= self.restart_interval

    def ensure_container_running(self) -> bool:
        """Ensure container is running, start if not"""
        if not self.is_container_running():
            logger.warning(f"[CONTAINER] Container not running, starting...")
            return self.start_container()
        return True

class DatabaseConnectionPool:
    """Thread-safe MySQL database connection pool for WAMP server"""
    def __init__(self, config: Dict, max_connections: int = 8):
        self.config = config
        self.max_connections = max_connections
        self._pool = Queue(maxsize=max_connections)
        self._lock = threading.Lock()
        self._created_connections = 0

    def _create_connection(self):
        """Create a new MySQL database connection"""
        try:
            conn = mysql.connector.connect(**self.config)
            conn.autocommit = False
            return conn
        except mysql.connector.Error as e:
            logger.error(f"Failed to create MySQL database connection: {e}")
            return None
        except Exception as e:
            logger.error(f"Failed to create database connection: {e}")
            return None

    @contextmanager
    def get_connection(self):
        """Get a connection from the pool"""
        conn = None
        try:
            # Try to get existing connection
            try:
                conn = self._pool.get_nowait()
            except Empty:
                # Create new connection if pool is empty and under limit
                with self._lock:
                    if self._created_connections < self.max_connections:
                        conn = self._create_connection()
                        if conn:
                            self._created_connections += 1
                        else:
                            raise Exception("Failed to create database connection")
                    else:
                        # Wait for available connection
                        conn = self._pool.get(timeout=30)

            # Test connection
            if conn and not self._test_connection(conn):
                conn = self._create_connection()

            yield conn

        except Exception as e:
            logger.error(f"Database connection error: {e}")
            yield None
        finally:
            # Return connection to pool
            if conn and conn.is_connected():
                try:
                    conn.rollback()  # Reset any pending transactions
                    self._pool.put_nowait(conn)
                except:
                    # If pool is full, close the connection
                    try:
                        conn.close()
                        with self._lock:
                            self._created_connections -= 1
                    except:
                        pass

    def _test_connection(self, conn) -> bool:
        """Test if MySQL connection is still valid"""
        try:
            if not conn.is_connected():
                return False
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.close()
            return True
        except mysql.connector.Error:
            return False
        except Exception:
            return False

    def close_all(self):
        """Close all connections in the pool"""
        while not self._pool.empty():
            try:
                conn = self._pool.get_nowait()
                conn.close()
            except:
                pass
        self._created_connections = 0



class ParallelGazetteScraper:
    """Enhanced Gazette Scraper with aggressive parallel processing capabilities"""

    def __init__(self, flaresolverr_url: str = "http://localhost:8191/v1", use_database: bool = True, manage_container: bool = True):
        self.flaresolverr_url = flaresolverr_url
        self.base_url = "https://www.thegazette.co.uk"
        self.session_pool = []  # Multiple sessions for aggressive processing
        self.session_index = 0
        self.results = []
        self.use_database = use_database
        self.manage_container = manage_container

        # Parallel processing attributes
        self.consecutive_failures = 0
        self.max_consecutive_failures = 3
        self.failed_inserts_log = "failed_database_inserts.log"
        self.processing_lock = threading.Lock()
        self.session_lock = threading.Lock()
        self._file_lock = threading.Lock()

        # Enhanced batch URL saving for 16GB RAM optimization
        self.pending_urls = []
        self.pending_urls_lock = threading.Lock()
        self.batch_save_size = 25  # Larger batches for 16GB RAM

        # Memory optimization features
        self.processed_cache = {}  # Cache for recently processed data
        self.cache_lock = threading.Lock()
        self.max_cache_size = PARALLEL_CONFIG.get('cache_size', 1000)

        # Performance monitoring
        self.processing_stats = {
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'start_time': time.time(),
            'last_batch_time': time.time()
        }
        self.stats_lock = threading.Lock()

        # FlareSolverr container management
        if manage_container:
            self.container_manager = FlareSolverrContainerManager()
            self._ensure_flaresolverr_ready()
        else:
            self.container_manager = None

        # Database connection pool
        if use_database:
            self.db_pool = DatabaseConnectionPool(MYSQL_DB_CONFIG.copy(), PARALLEL_CONFIG['max_db_connections'])
            self.table_columns = None
            self._initialize_database()
        else:
            self.db_pool = None

        # Session pool will be created lazily when needed
        self._sessions_initialized = False

    def _ensure_flaresolverr_ready(self):
        """Ensure FlareSolverr container is running and ready"""
        if not self.container_manager:
            return

        logger.info("[CONTAINER] Checking FlareSolverr container status...")

        if not self.container_manager.ensure_container_running():
            logger.error("[CONTAINER] Failed to ensure FlareSolverr container is running")
            raise Exception("FlareSolverr container management failed")

        logger.info("[CONTAINER] FlareSolverr container is ready")

    def _check_container_restart_needed(self) -> bool:
        """Check if FlareSolverr container needs to be restarted"""
        if not self.container_manager:
            return False

        return self.container_manager.should_restart()

    def _restart_flaresolverr_container(self) -> bool:
        """Restart FlareSolverr container and recreate sessions"""
        if not self.container_manager:
            return False

        logger.info("[CONTAINER] Scheduled FlareSolverr container restart...")

        # Destroy existing sessions
        self._destroy_all_sessions()

        # Restart container
        if not self.container_manager.restart_container():
            logger.error("[CONTAINER] Failed to restart FlareSolverr container")
            return False

        # Recreate sessions
        self._create_session_pool()

        logger.info("[CONTAINER] FlareSolverr container restart completed successfully")
        return True

    def _destroy_all_sessions(self):
        """Destroy all FlareSolverr sessions with improved error handling"""
        if not self.session_pool:
            logger.info("[CONTAINER] No sessions to destroy")
            return

        logger.info(f"[CONTAINER] Destroying {len(self.session_pool)} FlareSolverr sessions...")
        destroyed_count = 0

        for session_id in self.session_pool.copy():  # Use copy to avoid modification during iteration
            try:
                payload = {
                    "cmd": "sessions.destroy",
                    "session": session_id
                }
                response = requests.post(self.flaresolverr_url, json=payload, timeout=15)

                if response.status_code == 200:
                    result = response.json()
                    if result.get("status") == "ok":
                        destroyed_count += 1
                        logger.debug(f"[CONTAINER] Destroyed session: {session_id}")
                    else:
                        logger.warning(f"[CONTAINER] Failed to destroy session {session_id}: {result.get('message', 'Unknown error')}")
                else:
                    logger.warning(f"[CONTAINER] HTTP error destroying session {session_id}: {response.status_code}")

            except requests.exceptions.Timeout:
                logger.warning(f"[CONTAINER] Timeout destroying session {session_id}")
            except requests.exceptions.ConnectionError:
                logger.warning(f"[CONTAINER] Connection error destroying session {session_id} (FlareSolverr may be down)")
            except Exception as e:
                logger.warning(f"[CONTAINER] Error destroying session {session_id}: {e}")

        self.session_pool.clear()
        self.session_index = 0
        logger.info(f"[CONTAINER] Session cleanup completed: {destroyed_count}/{len(self.session_pool)} sessions destroyed")

    def _initialize_database(self):
        """Initialize MySQL database connection and fetch table schema"""
        if not self.use_database:
            return

        try:
            with self.db_pool.get_connection() as conn:
                if conn:
                    cursor = conn.cursor()
                    cursor.execute("""
                        SELECT column_name
                        FROM information_schema.columns
                        WHERE table_name = 'gazette_notice'
                        AND table_schema = %s
                        ORDER BY ordinal_position
                    """, (self.db_pool.config['database'],))
                    self.table_columns = [row[0] for row in cursor.fetchall()]
                    cursor.close()
                    logger.info(f"Fetched MySQL table columns: {len(self.table_columns)} columns")
                else:
                    logger.error("Failed to initialize MySQL database connection")
                    self.use_database = False
        except mysql.connector.Error as e:
            logger.error(f"MySQL error initializing database: {e}")
            self.use_database = False
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
            self.use_database = False

    def _ensure_sessions_initialized(self):
        """Ensure FlareSolverr sessions are initialized (lazy initialization)"""
        if self._sessions_initialized:
            return

        logger.info(f"Initializing {PARALLEL_CONFIG['session_pool_size']} FlareSolverr sessions for 16GB RAM optimization...")
        self._create_session_pool()
        self._sessions_initialized = True

    def _create_session_pool(self):
        """Create multiple FlareSolverr sessions for 16GB RAM optimized processing"""
        for i in range(PARALLEL_CONFIG['session_pool_size']):
            session_id = self._create_single_session(f"ram16gb_session_{i}_{int(time.time())}")
            if session_id:
                self.session_pool.append(session_id)
                logger.info(f"Created session {i+1}/{PARALLEL_CONFIG['session_pool_size']}: {session_id}")
            else:
                logger.warning(f"Failed to create session {i+1}")

        if not self.session_pool:
            logger.error("Failed to create any FlareSolverr sessions!")
        else:
            logger.info(f"Successfully created {len(self.session_pool)} sessions for 16GB RAM optimization")

    def _update_processing_stats(self, success: bool):
        """Update processing statistics for performance monitoring"""
        with self.stats_lock:
            self.processing_stats['total_processed'] += 1
            if success:
                self.processing_stats['successful'] += 1
            else:
                self.processing_stats['failed'] += 1

    def _get_processing_rate(self) -> float:
        """Calculate current processing rate (URLs per minute)"""
        with self.stats_lock:
            elapsed = time.time() - self.processing_stats['start_time']
            if elapsed > 0:
                return (self.processing_stats['total_processed'] / elapsed) * 60
            return 0.0

    def _manage_cache(self, url: str, data: Dict):
        """Manage memory cache for processed data"""
        if not PARALLEL_CONFIG.get('memory_optimization', False):
            return

        with self.cache_lock:
            # Add to cache
            self.processed_cache[url] = {
                'data': data,
                'timestamp': time.time()
            }

            # Cleanup old entries if cache is too large
            if len(self.processed_cache) > self.max_cache_size:
                # Remove oldest 20% of entries
                sorted_items = sorted(self.processed_cache.items(),
                                    key=lambda x: x[1]['timestamp'])
                items_to_remove = len(sorted_items) // 5
                for i in range(items_to_remove):
                    del self.processed_cache[sorted_items[i][0]]

    def _create_single_session(self, session_name: str) -> Optional[str]:
        """Create a single FlareSolverr session"""
        try:
            payload = {
                "cmd": "sessions.create",
                "session": session_name
            }
            response = requests.post(self.flaresolverr_url, json=payload, timeout=15)
            if response.status_code == 200:
                result = response.json()
                if result.get("status") == "ok":
                    return result.get("session")
            return None
        except Exception as e:
            logger.error(f"Error creating session {session_name}: {e}")
            return None

    def _get_session(self) -> Optional[str]:
        """Get next available session from pool (round-robin)"""
        # Ensure sessions are initialized before using them
        self._ensure_sessions_initialized()

        if not self.session_pool:
            return None

        with self.session_lock:
            session = self.session_pool[self.session_index % len(self.session_pool)]
            self.session_index += 1
            return session

    def make_request(self, url: str, timeout: int = None) -> Optional[str]:
        """Make aggressive request through FlareSolverr with retry logic"""
        if timeout is None:
            timeout = PARALLEL_CONFIG['timeout']

        for attempt in range(PARALLEL_CONFIG['max_retries']):
            session_id = self._get_session()
            if not session_id:
                logger.error("No available FlareSolverr sessions")
                return None

            try:
                # Add random micro-delay for anti-detection
                if PARALLEL_CONFIG['random_delays']:
                    random_delay = random.uniform(0.01, 0.05)
                    time.sleep(random_delay)

                payload = {
                    "cmd": "request.get",
                    "url": url,
                    "session": session_id,
                    "maxTimeout": timeout * 1000
                }

                # Add user agent rotation for anti-detection
                if PARALLEL_CONFIG['user_agent_rotation']:
                    payload["userAgent"] = random.choice(USER_AGENTS)

                response = requests.post(self.flaresolverr_url, json=payload, timeout=timeout + 5)

                if response.status_code == 200:
                    result = response.json()
                    if result.get("status") == "ok":
                        return result.get("solution", {}).get("response")
                    else:
                        error_msg = result.get('message', 'Unknown error')
                        if attempt < PARALLEL_CONFIG['max_retries'] - 1:
                            logger.warning(f"FlareSolverr error for {url} (attempt {attempt + 1}): {error_msg}, retrying...")
                            time.sleep(0.1)  # Brief pause before retry
                            continue
                        else:
                            logger.error(f"FlareSolverr error for {url} (final attempt): {error_msg}")
                else:
                    if attempt < PARALLEL_CONFIG['max_retries'] - 1:
                        logger.warning(f"HTTP error {response.status_code} for {url} (attempt {attempt + 1}), retrying...")
                        time.sleep(0.1)
                        continue
                    else:
                        logger.error(f"HTTP error {response.status_code} for {url} (final attempt)")

            except Exception as e:
                if attempt < PARALLEL_CONFIG['max_retries'] - 1:
                    logger.warning(f"Request error for {url} (attempt {attempt + 1}): {e}, retrying...")
                    time.sleep(0.1)
                    continue
                else:
                    logger.error(f"Request error for {url} (final attempt): {e}")

        return None

    def extract_notice_links(self, html: str) -> List[str]:
        """Extract notice links from listing page with enhanced filtering"""
        links = []
        try:
            soup = BeautifulSoup(html, 'html.parser')

            # Debug: Let's see what links are actually present
            all_links = soup.find_all('a', href=True)
            notice_hrefs = [link.get('href') for link in all_links if '/notice/' in link.get('href', '')]
            logger.debug(f"All notice hrefs found: {notice_hrefs[:10]}")  # Show first 10

            # Try multiple patterns for notice links (more specific patterns first)
            patterns = [
                r'/notice/[A-Z]-\d+-\d+(?:-\d+)?',  # Original pattern
                r'/notice/\d+',                     # Simple numeric pattern
                r'/notice/[A-Z0-9-]+',             # Alphanumeric with dashes
            ]

            for pattern in patterns:
                notice_links = soup.find_all('a', href=re.compile(pattern))
                logger.debug(f"Pattern '{pattern}' found {len(notice_links)} links")

                if notice_links:
                    for link in notice_links:
                        href = link.get('href')
                        if href and '/notice/' in href:
                            # Enhanced filtering to exclude metadata/signature URLs
                            exclude_patterns = [
                                '/share', 'facebook', 'twitter', 'google', 'linkedin',
                                '/data.xml', '/sig.xml', '/sig.trig', '/provenance',
                                'data_xml', 'data_rdf', 'signature', 'download=true'
                            ]

                            # Skip if URL contains any excluded patterns
                            if any(x in href for x in exclude_patterns):
                                logger.debug(f"Excluding metadata URL: {href}")
                                continue

                            # Clean the URL and validate it
                            clean_href = href.split('?')[0].rstrip('/')

                            # Check if it's a clean notice URL (no additional paths after notice ID)
                            # Valid patterns: /notice/123456 or /notice/L-123456-1
                            if (re.search(r'/notice/\d+$', clean_href) or
                                re.search(r'/notice/[A-Z]-\d+-\d+(?:-\d+)?$', clean_href)):

                                full_url = urljoin(self.base_url, clean_href)
                                if full_url not in links:
                                    links.append(full_url)
                                    logger.debug(f"Added clean notice URL: {full_url}")
                            else:
                                # This might be a notice URL with additional paths - check if it's metadata
                                if any(meta in clean_href for meta in ['data_xml', 'data_rdf', 'data.xml', 'sig.']):
                                    logger.debug(f"Filtered metadata URL: {clean_href}")
                                else:
                                    # It's a notice URL but with unexpected format - include it but log
                                    full_url = urljoin(self.base_url, clean_href)
                                    if full_url not in links:
                                        links.append(full_url)
                                        logger.debug(f"Added notice URL with unexpected format: {full_url}")

                    # If we found links with this pattern, use them
                    if links:
                        break

            logger.info(f"Found {len(links)} unique clean notice links")
            return links

        except Exception as e:
            logger.error(f"Error extracting links: {e}")
            return []

    def extract_metadata(self, soup: BeautifulSoup) -> Dict[str, str]:
        """Extract metadata from notice sidebar (same logic as original)"""
        metadata = {}

        # Enhanced metadata extraction with multiple selectors
        metadata_selectors = {
            'type': ['.notice-summary dt:contains("Type") + dd', '[data-gazettes="Type"]'],
            'notice_type': ['.notice-summary dt:contains("Notice type") + dd', '[data-gazettes="NoticeType"]'],
            'earliest_publish_date': ['.notice-summary dt:contains("Publication date") + dd', '[data-gazettes="PublicationDate"]'],
            'edition': ['.notice-summary dt:contains("Edition") + dd', '[data-gazettes="Edition"]'],
            'notice_id': ['.notice-summary dt:contains("Notice ID") + dd', '[data-gazettes="NoticeID"]'],
            'company_number': ['.notice-summary dt:contains("Company number") + dd', '[data-gazettes="CompanyNumber"]'],
            'notice_code': ['.notice-summary dt:contains("Notice code") + dd', '[data-gazettes="NoticeCode"]'],
            'issue_number': ['.notice-summary dt:contains("Issue number") + dd', '[data-gazettes="IssueNumber"]'],
            'page_number': ['.notice-summary dt:contains("Page number") + dd', '[data-gazettes="PageNumber"]']
        }

        for key, selectors in metadata_selectors.items():
            for selector in selectors:
                element = soup.select_one(selector)
                if element:
                    metadata[key] = element.get_text(strip=True)
                    break

        # Fallback: Extract from dl/dt/dd structure
        dl_elements = soup.select('.notice-summary > dl')
        for dl in dl_elements:
            dt_elements = dl.find_all('dt')
            for dt in dt_elements:
                dd = dt.find_next_sibling('dd')
                if dd:
                    key = dt.get_text(strip=True).lower().replace(' ', '_').replace(':', '')
                    value = dd.get_text(strip=True)

                    # Map common variations
                    key_mapping = {
                        'publication_date': 'earliest_publish_date',
                        'notice_id': 'notice_id',
                        'company_number': 'company_number',
                        'notice_code': 'notice_code',
                        'issue_number': 'issue_number',
                        'page_number': 'page_number'
                    }

                    if key in key_mapping:
                        metadata[key_mapping[key]] = value

                    # Also extract company number from timeline title if present
                    if 'timeline' in key and 'company' in key:
                        # Extract company number from text like "Notice timeline for COMPANY (12345678)"
                        company_match = re.search(r'\((\d{8})\)', value)
                        if company_match:
                            metadata['company_number'] = company_match.group(1)

        return metadata

    def extract_comprehensive_fields(self, soup: BeautifulSoup) -> Dict[str, str]:
        """Extract ALL possible fields using comprehensive patterns (same logic as original)"""
        data = {}

        # Initialize all fields to match database structure
        fields = [
            'timeline_title', 'notice_details_title', 'company_name', 'company_status',
            'company_type_of_liquidation', 'company_registered_office', 'principal_trading_address',
            'date_of_appointment', 'liquidator_name_and_address', 'insolvency_appointer',
            'court_name', 'case_code', 'trading_as', 'legislation_title', 'street_address',
            'locality', 'postal_code', 'nature_of_business', 'date_of_petition_presentation',
            'date_of_petition_presentation_day', 'petitioner_foaf_name', 'petitioner_street_address',
            'petitioner_locality', 'petitioner_postal_code', 'presented_by', 'name_of_place_of_hearing',
            'hearing_address', 'hearing_extended_address', 'hearing_locality',
            'hearing_postal_code', 'hearing_date', 'hearing_date_day', 'date_to_request_appearance',
            'date_to_request_appearance_day', 'ip_type', 'ip_foaf_name', 'ip_name', 'ip_address',
            'ip_locality', 'ip_region', 'ip_postal_code', 'ip_telephone', 'ip_email',
            'ip_has_ip_reference_number', 'description', 'signed_document_html',
            'signature_for_html_document', 'signed_rdf_document', 'signed_provenance_rdf'
        ]

        for field in fields:
            data[field] = None

        # Extract main content for text-based extraction
        main_content = soup.find('div', class_='full-notice') or soup.find('article') or soup.find('main')
        if main_content:
            content_text = main_content.get_text(separator=' ', strip=True)
            data['description'] = content_text
            data['full_text'] = content_text

            # Apply comprehensive extraction methods (same as original)
            self._extract_company_details_comprehensive(content_text, data)
            self._extract_liquidation_details_comprehensive(content_text, data)
            self._extract_court_details_comprehensive(content_text, data)
            self._extract_dates_and_numbers_comprehensive(content_text, data)

        # Extract from HTML structure
        self._extract_from_html_structure(soup, data)

        # Extract timeline title
        timeline_title = soup.select_one('.main-pane > aside > .notice-timeline > h2')
        if timeline_title:
            data['timeline_title'] = timeline_title.get_text(strip=True)

        # Extract notice details title
        notice_title = soup.select_one('.full-notice > header > h1')
        if notice_title:
            data['notice_details_title'] = notice_title.get_text(strip=True)

        # Extract digital signatures
        self._extract_digital_signatures(soup, data)

        # Set defaults and clean up
        self._finalize_comprehensive_data(data)

        return data

    def _extract_company_details_comprehensive(self, content_text: str, data: Dict):
        """Enhanced company details extraction (same logic as original)"""

        # Company Name - Multiple patterns
        company_patterns = [
            r'Name of Company[:\-—]\s*([A-Z][A-Z0-9\s&\(\)\.,-]+?)\s*(?:\.|Company|\()',
            r'([A-Z][A-Z0-9\s&\(\)\.,-]+?LTD)\s*(?:\(|$)',
            r'([A-Z][A-Z0-9\s&\(\)\.,-]+?LIMITED)\s*(?:\(|$)',
            r'([A-Z][A-Z0-9\s&\(\)\.,-]+?PLC)\s*(?:\(|$)',
            r'([A-Z][A-Z0-9\s&\(\)\.,-]{5,}?)\s*\((?:formerly|In Liquidation)',
            r'In the Matter of\s+([A-Z][A-Z0-9\s&\(\)\.,-]+?)\s*\(',
            r'Notice to Creditors\s+([A-Z][A-Z0-9\s&\(\)\.,-]+?)\s*\(',
            r'Final Meetings\s+([A-Z][A-Z0-9\s&\(\)\.,-]+?)\s*\(',
            r'Meetings of Creditors\s+([A-Z][A-Z0-9\s&\(\)\.,-]+?)\s*\(',
        ]

        for pattern in company_patterns:
            match = re.search(pattern, content_text, re.IGNORECASE)
            if match:
                company_name = match.group(1).strip()
                if (len(company_name) > 3 and
                    not company_name.lower().startswith(('type', 'nature', 'address', 'court', 'date', 'creditors', 'members', 'in the', 'matter')) and
                    not company_name.lower() in ['voluntary', 'liquidation']):
                    data["company_name"] = company_name
                    break

        # Company Registration Number - Enhanced patterns
        reg_patterns = [
            r'Company Number[:\-—\s]*([A-Z0-9]{6,})',
            r'Company Registration Number[:\-—\s]*([A-Z0-9]{6,})',
            r'\(Company Number\s+([A-Z0-9]{6,})\)',
            r'Registration Number[:\-—\s]*([A-Z0-9]{6,})',
            r'Company Number:\s*([A-Z0-9]{6,})',
        ]

        for pattern in reg_patterns:
            match = re.search(pattern, content_text, re.IGNORECASE)
            if match:
                company_number = match.group(1).strip()
                if len(company_number) >= 6 and company_number.isalnum():
                    data["company_number"] = company_number
                    break

    def _extract_liquidation_details_comprehensive(self, content_text: str, data: Dict):
        """Extract liquidation details (simplified for space)"""
        # Liquidator name patterns
        liquidator_patterns = [
            r'Liquidator[\'s]*\s*name\s*and\s*address[:\-—]\s*([^\.]+?)(?:\s+\(IP|Date|By whom)',
            r'Joint Liquidator[:\-—]\s*([^\.]+?)(?:\s+\(IP|Date|By whom)',
            r'Office Holder[:\-—]\s*([^\.]+?)(?:\s+\(IP|Date|By whom)',
        ]

        for pattern in liquidator_patterns:
            match = re.search(pattern, content_text, re.IGNORECASE)
            if match:
                liquidator = match.group(1).strip()
                if len(liquidator) > 3:
                    data["liquidator_name_and_address"] = liquidator
                    data["ip_foaf_name"] = liquidator.split('(')[0].strip()
                    data["ip_name"] = liquidator.split('(')[0].strip()
                    break

    def _extract_court_details_comprehensive(self, content_text: str, data: Dict):
        """Extract court details (simplified for space)"""
        court_patterns = [
            r'Court[:\-—]\s*([^\.]+?)(?:\s+(?:Case|Matter|Petition))',
            r'In the\s+([^\.]+?Court[^\.]*?)(?:\s+(?:Case|Matter|Petition))',
        ]

        for pattern in court_patterns:
            match = re.search(pattern, content_text, re.IGNORECASE)
            if match:
                court = match.group(1).strip()
                if len(court) > 3:
                    data["court_name"] = court
                    break

    def _extract_dates_and_numbers_comprehensive(self, content_text: str, data: Dict):
        """Extract dates and numbers (simplified for space)"""
        # Date of appointment
        date_patterns = [
            r'Date of Appointment[:\-—]\s*(\d{1,2}\s+\w+\s+\d{4})',
            r'Appointed[:\-—]\s*(\d{1,2}\s+\w+\s+\d{4})',
        ]

        for pattern in date_patterns:
            match = re.search(pattern, content_text, re.IGNORECASE)
            if match:
                date_str = match.group(1).strip()
                data["date_of_appointment"] = self._parse_date_string(date_str)
                break

    def _extract_from_html_structure(self, soup: BeautifulSoup, data: Dict):
        """Extract from HTML structure (simplified for space)"""
        # Extract registered office
        office_elem = soup.find(string=re.compile(r'Registered office', re.I))
        if office_elem:
            parent = office_elem.parent
            if parent:
                office_text = parent.get_text(strip=True)
                data["company_registered_office"] = office_text

    def _extract_digital_signatures(self, soup: BeautifulSoup, data: Dict):
        """Extract digital signature links (same logic as original)"""
        # Look for download links
        download_links = soup.find_all('a', href=True)
        for link in download_links:
            href = link.get('href', '')
            if 'data.xml' in href and 'download=true' in href:
                data['signed_document_html'] = href
            elif 'sig.xml' in href and 'download=true' in href:
                data['signature_for_html_document'] = href
            elif 'sig.trig' in href and 'download=true' in href:
                if 'provenance' in href:
                    data['signed_provenance_rdf'] = href
                else:
                    data['signed_rdf_document'] = href

    def _finalize_comprehensive_data(self, data: Dict):
        """Finalize and clean up extracted data (same logic as original)"""
        # Set category_name
        data["category_name"] = "Corporate Insolvency"

    def _parse_date_string(self, date_str: str) -> Optional[str]:
        """Parse date string to standard format"""
        try:
            # Try different date formats
            formats = ['%d %B %Y', '%d %b %Y', '%d/%m/%Y', '%Y-%m-%d']
            for fmt in formats:
                try:
                    dt = datetime.strptime(date_str.strip(), fmt)
                    return dt.strftime('%Y-%m-%d')
                except ValueError:
                    continue
        except:
            pass
        return None

    def insert_notice_to_database(self, notice_data: Dict) -> bool:
        """Thread-safe MySQL database insertion with connection pooling"""
        if not self.use_database or not self.table_columns:
            return False

        try:
            with self.db_pool.get_connection() as conn:
                if not conn:
                    return False

                cursor = conn.cursor()

                # Prepare data for database insertion
                db_data = self.prepare_data_for_database(notice_data)

                # Filter data to only include columns that exist in the table
                filtered_data = {k: v for k, v in db_data.items() if k in self.table_columns}

                if not filtered_data:
                    logger.warning("No matching columns found in the database for the provided data.")
                    return False

                # Create INSERT query with MySQL placeholders
                columns = list(filtered_data.keys())
                placeholders = ', '.join(['%s'] * len(columns))
                query = f"INSERT INTO gazette_notice ({', '.join(columns)}) VALUES ({placeholders})"

                # Execute query
                cursor.execute(query, list(filtered_data.values()))
                conn.commit()

                notice_id = cursor.lastrowid  # MySQL uses lastrowid instead of RETURNING
                cursor.close()

                logger.info(f"Successfully inserted notice into MySQL database with ID: {notice_id}")

                # Reset failure counter on success
                with self.processing_lock:
                    self.consecutive_failures = 0

                return True

        except mysql.connector.Error as e:
            logger.error(f"MySQL error inserting notice into database: {e}")

            # Log failed insert to file
            self._log_failed_insert(notice_data, str(e))

            # Track consecutive failures
            with self.processing_lock:
                self.consecutive_failures += 1
                logger.warning(f"Consecutive database failures: {self.consecutive_failures}/{self.max_consecutive_failures}")

            return False
        except Exception as e:
            logger.error(f"Error inserting notice into database: {e}")

            # Log failed insert to file
            self._log_failed_insert(notice_data, str(e))

            # Track consecutive failures
            with self.processing_lock:
                self.consecutive_failures += 1
                logger.warning(f"Consecutive database failures: {self.consecutive_failures}/{self.max_consecutive_failures}")

            return False

    def _log_failed_insert(self, notice_data: Dict, error_message: str):
        """Log failed database insert to file (thread-safe)"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            log_entry = {
                'timestamp': timestamp,
                'error': error_message,
                'notice_data': notice_data
            }

            # Thread-safe file writing
            with threading.Lock():
                with open(self.failed_inserts_log, 'a', encoding='utf-8') as f:
                    f.write(json.dumps(log_entry, default=str, ensure_ascii=False) + '\n')

            logger.info(f"Failed insert logged to {self.failed_inserts_log}")
        except Exception as e:
            logger.error(f"Failed to log failed insert: {e}")

    def prepare_data_for_database(self, notice_data: Dict) -> Dict:
        """Prepare notice data for database insertion"""
        db_data = notice_data.copy()

        # Convert date and datetime fields
        date_fields = ['date_of_appointment']
        datetime_fields = ['earliest_publish_date']

        for field in date_fields:
            if field in db_data:
                db_data[field] = self._parse_date(db_data.get(field))

        for field in datetime_fields:
            if field in db_data:
                db_data[field] = self._parse_datetime(db_data.get(field))

        # Convert integer fields
        int_fields = ['notice_code']
        for field in int_fields:
            if field in db_data:
                db_data[field] = self._safe_int(db_data.get(field))

        # Clean up string fields
        for key, value in db_data.items():
            if isinstance(value, str) and (not value.strip() or value.strip().lower() == 'null'):
                db_data[key] = None

        # Set timestamps
        now = datetime.now()
        db_data['created_at'] = now
        db_data['updated_at'] = now

        # Remove fields that are not part of the DB schema
        db_data.pop('notice_url', None)
        db_data.pop('scraped_at', None)
        db_data.pop('full_text', None)

        return db_data

    def _safe_int(self, value) -> Optional[int]:
        """Safely convert value to integer"""
        if value is None or value == '':
            return None
        try:
            return int(str(value).strip())
        except (ValueError, TypeError):
            return None

    def _parse_datetime(self, date_str) -> Optional[datetime]:
        """Parse datetime string to datetime object"""
        if not date_str or str(date_str).strip() == '' or date_str == 'null':
            return None

        try:
            date_str = str(date_str).strip()
            formats = [
                '%d %B %Y',
                '%Y-%m-%d',
                '%d/%m/%Y',
            ]

            for fmt in formats:
                try:
                    return datetime.strptime(date_str, fmt)
                except ValueError:
                    continue

            logger.warning(f"Could not parse datetime: {date_str}")
            return None

        except Exception as e:
            logger.warning(f"Error parsing datetime '{date_str}': {e}")
            return None

    def _parse_date(self, date_str) -> Optional[datetime]:
        """Parse date string to date object"""
        dt = self._parse_datetime(date_str)
        return dt.date() if dt else None

    def process_single_notice(self, url: str, url_index: int = 0) -> ProcessingResult:
        """Process a single notice URL (thread-safe)"""
        try:
            logger.info(f"Processing notice: {url}")

            html = self.make_request(url, timeout=PARALLEL_CONFIG['timeout'])
            if not html:
                error_msg = f"Failed to fetch HTML for: {url}"
                logger.error(error_msg)
                return ProcessingResult(url=url, success=False, error=error_msg, url_index=url_index)

            soup = BeautifulSoup(html, 'html.parser')

            # Check if notice summary exists
            if not soup.select('.notice-summary > dl'):
                error_msg = f"No notice summary found for: {url}"
                logger.error(error_msg)

                # Log failed URL to failed_urls.log
                failed_logger.info(f"FAILED_URL - URL {url_index + 1} - {url} - {error_msg}")

                return ProcessingResult(url=url, success=False, error=error_msg, url_index=url_index)

            # Extract all data using comprehensive methods
            metadata = self.extract_metadata(soup)
            detailed_data = self.extract_comprehensive_fields(soup)

            # Combine all data
            result = detailed_data.copy()
            result.update(metadata)

            # Add other relevant info
            result['notice_url'] = url
            result['scraped_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            result['category_name'] = metadata.get('type', 'Corporate Insolvency')

            # Clean up company number
            if result.get('company_number'):
                company_num = str(result.get('company_number'))
                clean_match = re.search(r'(\d{8})', company_num)
                if clean_match:
                    result['company_number'] = clean_match.group(1)
                elif len(company_num) >= 6 and company_num.isalnum():
                    result['company_number'] = company_num

            # Save to database if enabled
            db_success = True
            if self.use_database:
                db_success = self.insert_notice_to_database(result)
                if not db_success:
                    # Check if we should stop due to consecutive failures
                    with self.processing_lock:
                        if self.consecutive_failures >= self.max_consecutive_failures:
                            error_msg = f"Reached maximum consecutive database failures ({self.max_consecutive_failures})"
                            logger.error(error_msg)
                            return ProcessingResult(url=url, success=False, error=error_msg, url_index=url_index)

            return ProcessingResult(url=url, success=True, data=result, url_index=url_index)

        except Exception as e:
            error_msg = f"Error processing {url}: {str(e)}"
            logger.error(error_msg)
            return ProcessingResult(url=url, success=False, error=error_msg, url_index=url_index)

    def load_urls_from_file(self, filename: str = "urls.txt") -> List[str]:
        """Load URLs from a text file"""
        urls = []
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                for line in f:
                    url = line.strip()
                    if url and not url.startswith('#'):  # Skip empty lines and comments
                        urls.append(url)
            logger.info(f"Loaded {len(urls)} URLs from {filename}")
            return urls
        except FileNotFoundError:
            logger.error(f"URLs file not found: {filename}")
            return []
        except Exception as e:
            logger.error(f"Error reading URLs file {filename}: {e}")
            return []

    def load_processed_urls(self, filename: str = PROCESSED_URLS_FILE) -> set:
        """Load already processed URLs from file"""
        processed_urls = set()
        try:
            if os.path.exists(filename):
                with open(filename, 'r', encoding='utf-8') as f:
                    for line in f:
                        url = line.strip()
                        if url:
                            processed_urls.add(url)
                logger.info(f"Loaded {len(processed_urls)} processed URLs from {filename}")
            else:
                logger.info(f"No processed URLs file found ({filename}), starting fresh")
            return processed_urls
        except Exception as e:
            logger.error(f"Error reading processed URLs file {filename}: {e}")
            return set()

    def save_processed_url(self, url: str, filename: str = PROCESSED_URLS_FILE):
        """Save a processed URL to file (thread-safe with immediate flush)"""
        try:
            # Use class-level lock for thread safety
            if not hasattr(self, '_file_lock'):
                self._file_lock = threading.Lock()

            with self._file_lock:
                with open(filename, 'a', encoding='utf-8', buffering=1) as f:  # Line buffering
                    f.write(f"{url}\n")
                    f.flush()  # Force immediate write to disk
                    os.fsync(f.fileno())  # Force OS to write to disk

            logger.debug(f"Saved processed URL: {url}")
        except Exception as e:
            logger.error(f"Error saving processed URL to {filename}: {e}")
            # Try alternative approach
            try:
                with open(filename + ".backup", 'a', encoding='utf-8') as f:
                    f.write(f"{url}\n")
                    f.flush()
                logger.warning(f"Saved to backup file: {filename}.backup")
            except Exception as backup_error:
                logger.error(f"Failed to save to backup file: {backup_error}")

    def add_processed_url_to_batch(self, url: str):
        """Add URL to batch for saving (more efficient for high-volume processing)"""
        try:
            with self.pending_urls_lock:
                self.pending_urls.append(url)

                # Save batch when it reaches the threshold
                if len(self.pending_urls) >= self.batch_save_size:
                    self._save_url_batch()

        except Exception as e:
            logger.error(f"Error adding URL to batch: {e}")
            # Fallback to immediate save
            self.save_processed_url(url)

    def _save_url_batch(self):
        """Save batch of URLs to file (called with pending_urls_lock held)"""
        if not self.pending_urls:
            return

        try:
            urls_to_save = self.pending_urls.copy()
            self.pending_urls.clear()

            # Save all URLs in batch
            with self._file_lock:
                with open(PROCESSED_URLS_FILE, 'a', encoding='utf-8', buffering=1) as f:
                    for url in urls_to_save:
                        f.write(f"{url}\n")
                    f.flush()
                    os.fsync(f.fileno())

            logger.info(f"Batch saved {len(urls_to_save)} processed URLs")

        except Exception as e:
            logger.error(f"Error saving URL batch: {e}")
            # Fallback: save individually
            for url in urls_to_save:
                self.save_processed_url(url)

    def flush_pending_urls(self):
        """Force save any pending URLs"""
        try:
            with self.pending_urls_lock:
                if self.pending_urls:
                    self._save_url_batch()
                    logger.info("Flushed all pending URLs to file")
        except Exception as e:
            logger.error(f"Error flushing pending URLs: {e}")

    def get_unprocessed_urls(self, all_urls: List[str]) -> List[str]:
        """Get list of URLs that haven't been processed yet"""
        processed_urls = self.load_processed_urls()
        unprocessed_urls = [url for url in all_urls if url not in processed_urls]

        logger.info(f"Total URLs: {len(all_urls)}")
        logger.info(f"Already processed: {len(processed_urls)}")
        logger.info(f"Remaining to process: {len(unprocessed_urls)}")

        return unprocessed_urls

    def show_progress_stats(self, filename: str = "urls.txt"):
        """Show progress statistics"""
        try:
            all_urls = self.load_urls_from_file(filename)
            processed_urls = self.load_processed_urls()

            total_urls = len(all_urls)
            processed_count = len(processed_urls)
            remaining_count = total_urls - processed_count
            progress_percent = (processed_count / total_urls * 100) if total_urls > 0 else 0

            logger.info(f"PROGRESS STATISTICS:")
            logger.info(f"   Total URLs: {total_urls:,}")
            logger.info(f"   Processed: {processed_count:,} ({progress_percent:.1f}%)")
            logger.info(f"   Remaining: {remaining_count:,}")
            logger.info(f"   Processed URLs file: {PROCESSED_URLS_FILE}")

            return {
                'total_urls': total_urls,
                'processed_count': processed_count,
                'remaining_count': remaining_count,
                'progress_percent': progress_percent
            }
        except Exception as e:
            logger.error(f"Error showing progress stats: {e}")
            return {}

    def process_urls_parallel(self, urls: List[str], start_index: int = 0) -> List[ProcessingResult]:
        """Process multiple URLs in parallel with controlled concurrency"""
        results = []

        # Filter URLs to process (skip already processed ones)
        urls_to_process = []
        for i, url in enumerate(urls):
            if i >= start_index:
                urls_to_process.append((url, i))
            else:
                logger.info(f"Skipping URL {i + 1} (already processed)")

        if not urls_to_process:
            logger.info("No URLs to process")
            return results

        logger.info(f"Processing {len(urls_to_process)} URLs in parallel (16GB RAM optimized)")
        logger.info(f"Configuration: {PARALLEL_CONFIG['max_workers']} workers, {PARALLEL_CONFIG['batch_size']} batch size")
        logger.info(f"Memory optimization: {PARALLEL_CONFIG.get('memory_optimization', False)}")

        # Process URLs in larger batches for 16GB RAM optimization
        batch_size = PARALLEL_CONFIG['batch_size']

        for batch_start in range(0, len(urls_to_process), batch_size):
            batch_end = min(batch_start + batch_size, len(urls_to_process))
            batch_urls = urls_to_process[batch_start:batch_end]

            batch_num = batch_start//batch_size + 1
            total_batches = (len(urls_to_process) + batch_size - 1) // batch_size

            # Performance monitoring
            current_rate = self._get_processing_rate()
            logger.info(f"Processing batch {batch_num}/{total_batches}: URLs {batch_start + 1}-{batch_end}")
            logger.info(f"Current rate: {current_rate:.1f} URLs/min | Memory cache: {len(self.processed_cache)} items")

            # Check if FlareSolverr container needs restart (every 4 hours)
            if self._check_container_restart_needed():
                logger.info("[CONTAINER] 4-hour interval reached, restarting FlareSolverr container...")
                if not self._restart_flaresolverr_container():
                    logger.error("[CONTAINER] Failed to restart container, continuing with existing setup")

            # Check if we should stop due to consecutive failures
            with self.processing_lock:
                if self.consecutive_failures >= self.max_consecutive_failures:
                    logger.error("Stopping due to consecutive database failures")
                    break

            # Process batch in parallel
            with concurrent.futures.ThreadPoolExecutor(max_workers=PARALLEL_CONFIG['max_workers']) as executor:
                # Submit all tasks in the batch
                future_to_url = {}
                for url, url_index in batch_urls:
                    future = executor.submit(self.process_single_notice, url, url_index)
                    future_to_url[future] = (url, url_index)

                # Collect results as they complete
                batch_results = []
                for future in concurrent.futures.as_completed(future_to_url, timeout=PARALLEL_CONFIG['timeout'] * 2):
                    url, url_index = future_to_url[future]
                    try:
                        result = future.result()
                        batch_results.append(result)

                        if result.success:
                            logger.info(f"[SUCCESS] Successfully processed URL {url_index + 1}: {url}")
                            # Add to batch for efficient saving
                            self.add_processed_url_to_batch(url)
                            # Update stats and manage cache
                            self._update_processing_stats(True)
                            if result.data:
                                self._manage_cache(url, result.data)
                        else:
                            logger.error(f"[FAILED] Failed to process URL {url_index + 1}: {url} - {result.error}")

                            # Enhanced failed URL logging with more context
                            if "No notice summary found" in result.error:
                                # This is likely a metadata/signature URL that should be filtered
                                failed_logger.info(f"METADATA_URL - URL {url_index + 1} - {url} - {result.error}")
                                logger.warning(f"[METADATA] Metadata URL detected (should be filtered): {url}")
                            else:
                                # This is a genuine failure
                                failed_logger.info(f"FAILED_URL - URL {url_index + 1} - {url} - {result.error}")

                            # Still save as processed to avoid reprocessing failed URLs
                            self.add_processed_url_to_batch(url)
                            # Update stats
                            self._update_processing_stats(False)

                    except Exception as e:
                        logger.error(f"[EXCEPTION] Exception processing URL {url_index + 1}: {url} - {e}")
                        failed_logger.info(f"EXCEPTION_URL - URL {url_index + 1} - {url} - {e}")
                        # Save as processed even for exceptions to avoid reprocessing
                        self.add_processed_url_to_batch(url)

                # Sort batch results by URL index to maintain order
                batch_results.sort(key=lambda x: x.url_index)
                results.extend(batch_results)

                # Flush any pending URLs after each batch
                self.flush_pending_urls()

                # Minimal delay between batches for aggressive processing
                if batch_end < len(urls_to_process):
                    delay = PARALLEL_CONFIG['request_delay']  # Much shorter delay
                    logger.info(f"Brief pause {delay}s before next batch...")
                    time.sleep(delay)

        return results

    def process_urls_from_file(self, filename: str = "urls.txt", force_restart: bool = False):
        """Process URLs from a text file with parallel processing and resume capability"""
        logger.info(f"==================== Processing URLs from {filename} ====================")

        # Load all URLs from file
        all_urls = self.load_urls_from_file(filename)

        if not all_urls:
            logger.error("No URLs found to process")
            return

        # Show initial progress statistics
        self.show_progress_stats(filename)

        # Get unprocessed URLs (unless force restart is requested)
        if force_restart:
            logger.info("[RESTART] Force restart requested - processing all URLs")
            urls_to_process = all_urls
            # Clear processed URLs file
            try:
                if os.path.exists(PROCESSED_URLS_FILE):
                    os.remove(PROCESSED_URLS_FILE)
                    logger.info(f"Cleared processed URLs file: {PROCESSED_URLS_FILE}")
            except Exception as e:
                logger.warning(f"Could not clear processed URLs file: {e}")
        else:
            urls_to_process = self.get_unprocessed_urls(all_urls)

        if not urls_to_process:
            logger.info("[COMPLETE] All URLs have already been processed!")
            return

        logger.info(f"[INFO] URLs to process: {len(urls_to_process)}")

        # Process URLs in parallel
        results = self.process_urls_parallel(urls_to_process, 0)

        # Update results
        successful_results = [r for r in results if r.success and r.data]
        self.results.extend([r.data for r in successful_results])

        # Final flush of any remaining URLs
        self.flush_pending_urls()

        logger.info(f"[COMPLETE] Completed processing URLs from {filename}")
        logger.info(f"Successfully processed: {len(successful_results)}/{len(results)} URLs")

        # Show final statistics
        final_stats = self.show_progress_stats(filename)
        if final_stats:
            logger.info(f"[STATS] Final progress: {final_stats['processed_count']}/{final_stats['total_urls']} URLs ({final_stats['progress_percent']:.1f}%)")

        # Check if we should stop due to consecutive failures
        with self.processing_lock:
            if self.consecutive_failures >= self.max_consecutive_failures:
                raise Exception(f"Reached maximum consecutive database failures ({self.max_consecutive_failures})")



    def cleanup(self):
        """Cleanup resources"""
        try:
            # Flush any remaining pending URLs before cleanup
            logger.info("[CLEANUP] Flushing any pending processed URLs...")
            self.flush_pending_urls()

            # Destroy all FlareSolverr sessions
            self._destroy_all_sessions()
        except Exception as e:
            logger.error(f"Error during session cleanup: {e}")

        # Close database connections
        if self.db_pool:
            self.db_pool.close_all()
            logger.info("Database connections closed")

        # Note: We don't stop the FlareSolverr container here as it might be used by other processes
        # The container will continue running for potential future use

def signal_handler(signum, frame):
    """Handle shutdown signals gracefully"""
    _ = frame  # Unused parameter
    logger.info(f"\n[SIGNAL] Received signal {signum}, initiating graceful shutdown...")
    # The cleanup will be handled in the finally block
    raise KeyboardInterrupt("Graceful shutdown requested")

def main():
    """Main function with parallel processing for URL file processing"""
    from argparse import ArgumentParser

    # Set up signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    parser = ArgumentParser()
    parser.add_argument('--flaresolverr-url', default='http://localhost:8191/v1')
    parser.add_argument('--no-database', action='store_true')
    parser.add_argument('--db-host', default='localhost')
    parser.add_argument('--db-port', type=int, default=3306)  # MySQL default port
    parser.add_argument('--db-user', default='root')  # WAMP default user
    parser.add_argument('--db-password', default='')  # WAMP default password (empty)
    parser.add_argument('--db-name', default='gazette_scraper', help='MySQL database name')
    parser.add_argument('--max-workers', type=int, default=12, help='Maximum parallel workers (optimized for 16GB RAM)')
    parser.add_argument('--batch-size', type=int, default=25, help='Batch size for parallel processing (optimized for 16GB RAM)')
    parser.add_argument('--urls-file', default='urls.txt', help='File containing URLs to process')
    parser.add_argument('--force-restart', action='store_true', help='Force restart from beginning, ignoring processed URLs')
    parser.add_argument('--show-progress', action='store_true', help='Show progress statistics and exit')
    parser.add_argument('--no-container-management', action='store_true', help='Disable automatic FlareSolverr container management')
    parser.add_argument('--container-restart-hours', type=int, default=4, help='Hours between FlareSolverr container restarts')
    args = parser.parse_args()

    # Update parallel configuration based on arguments
    PARALLEL_CONFIG['max_workers'] = args.max_workers
    PARALLEL_CONFIG['batch_size'] = args.batch_size

    # Update FlareSolverr container configuration
    if not args.no_container_management:
        FLARESOLVERR_CONFIG['restart_interval_hours'] = args.container_restart_hours

    logger.info(f"PARALLEL CONFIG:")
    logger.info(f"   Workers: {PARALLEL_CONFIG['max_workers']}")
    logger.info(f"   Batch Size: {PARALLEL_CONFIG['batch_size']}")
    logger.info(f"   Sessions: {PARALLEL_CONFIG['session_pool_size']}")
    logger.info(f"   URLs File: {args.urls_file}")
    logger.info(f"   Force Restart: {args.force_restart}")
    logger.info(f"   Processed URLs File: {PROCESSED_URLS_FILE}")

    if not args.no_container_management:
        logger.info(f"CONTAINER MANAGEMENT:")
        logger.info(f"   Container Name: {FLARESOLVERR_CONFIG['container_name']}")
        logger.info(f"   Restart Interval: {FLARESOLVERR_CONFIG['restart_interval_hours']} hours")
        logger.info(f"   Wait After Restart: {FLARESOLVERR_CONFIG['wait_after_restart_minutes']} minutes")
    else:
        logger.info(f"CONTAINER MANAGEMENT: Disabled")

    # Handle show progress option
    if args.show_progress:
        scraper = ParallelGazetteScraper(args.flaresolverr_url, False, False)  # No database, no container management
        scraper.show_progress_stats(args.urls_file)
        return

    use_database = not args.no_database
    manage_container = not args.no_container_management
    scraper = ParallelGazetteScraper(args.flaresolverr_url, use_database, manage_container)

    if use_database:
        # Update MySQL database configuration
        scraper.db_pool.config.update({
            'host': args.db_host,
            'port': args.db_port,
            'user': args.db_user,
            'password': args.db_password,
            'database': args.db_name
        })

        # Test MySQL database connection
        with scraper.db_pool.get_connection() as conn:
            if not conn:
                logger.error("[ERROR] MySQL database connection failed.")
                logger.error("Make sure WAMP server is running and MySQL service is started.")
                logger.error(f"Connection details: {args.db_user}@{args.db_host}:{args.db_port}/{args.db_name}")
                return

        logger.info(f"[SUCCESS] Connected to MySQL database: {args.db_user}@{args.db_host}:{args.db_port}/{args.db_name}")
        logger.info(f"[INFO] MySQL connection pool: {PARALLEL_CONFIG['max_db_connections']} max connections")
    else:
        logger.info("[INFO] Database disabled, no data will be saved.")

    try:
        # Process URLs from file
        logger.info("[START] STARTING URL FILE PROCESSING MODE")
        logger.info(f"[CONFIG] Configuration: {PARALLEL_CONFIG['max_workers']} workers, {PARALLEL_CONFIG['batch_size']} batch size")
        logger.info(f"[CONFIG] Session pool: {len(scraper.session_pool)} sessions")

        scraper.process_urls_from_file(args.urls_file, args.force_restart)

        logger.info("[COMPLETE] URL processing completed!")

    except KeyboardInterrupt:
        logger.info("\n[INTERRUPT] Scraping interrupted by user.")
    except Exception as e:
        logger.error(f"[ERROR] Unexpected error during processing: {e}")
        import traceback
        logger.error(f"[ERROR] Traceback: {traceback.format_exc()}")
    finally:
        logger.info("[CLEANUP] Starting cleanup process...")
        try:
            scraper.cleanup()
            logger.info("[CLEANUP] Cleanup completed successfully")
        except Exception as cleanup_error:
            logger.error(f"[CLEANUP] Error during cleanup: {cleanup_error}")

if __name__ == '__main__':
    main()
